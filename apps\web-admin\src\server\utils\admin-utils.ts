/**
 * @file apps/web-admin/src/server/utils/admin-utils.ts
 * @description Admin-specific server utilities
 */

import 'server-only';
import { headers } from 'next/headers';
import { getCurrentAdminUser } from '@encreasl/auth/server';
import type { 
  AdminServerActionResult, 
  AdminServerError, 
  AdminRequestContext 
} from '../types/admin-server-types';

// ========================================
// ADMIN REQUEST VALIDATION
// ========================================

/**
 * Validate and extract admin request context
 */
export async function validateAdminRequest(): Promise<AdminRequestContext> {
  const headersList = await headers();
  const adminUser = await getCurrentAdminUser();
  
  // Extract admin permissions and roles from custom claims
  const permissions = adminUser.customClaims?.permissions || [];
  const roles = adminUser.customClaims?.roles || [];
  
  return {
    adminId: adminUser.uid,
    sessionId: crypto.randomUUID(), // In real app, this would come from session
    permissions: Array.isArray(permissions) ? permissions : [],
    roles: Array.isArray(roles) ? roles : [],
    userAgent: headersList.get('user-agent') || undefined,
    ipAddress: headersList.get('x-forwarded-for') || 
               headersList.get('x-real-ip') || 
               'unknown',
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID(),
  };
}

/**
 * Check if admin has required permissions
 */
export async function checkAdminPermissions(
  requiredPermissions: string[]
): Promise<{ hasPermission: boolean; missingPermissions: string[] }> {
  try {
    const context = await validateAdminRequest();
    const missingPermissions = requiredPermissions.filter(
      permission => !context.permissions.includes(permission)
    );
    
    return {
      hasPermission: missingPermissions.length === 0,
      missingPermissions,
    };
  } catch {
    return {
      hasPermission: false,
      missingPermissions: requiredPermissions,
    };
  }
}

/**
 * Sanitize admin input to prevent XSS and other attacks
 */
export function sanitizeAdminInput(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove basic HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 10000); // Limit length
}

/**
 * Format a standardized admin response
 */
export function formatAdminResponse<T>(
  success: boolean,
  data?: T,
  message?: string,
  error?: AdminServerError,
  metadata?: {
    timestamp: string;
    adminId: string;
    action: string;
    resourceId?: string;
  }
): AdminServerActionResult<T> {
  return {
    success,
    data,
    message,
    error,
    metadata,
  };
}

/**
 * Handle admin server errors consistently
 */
export function handleAdminError(
  error: unknown,
  defaultCode: string = 'ADMIN_INTERNAL_SERVER_ERROR'
): AdminServerActionResult<never> {
  console.error('Admin server error:', error);
  
  let adminError: AdminServerError;
  
  if (error instanceof Error) {
    adminError = {
      code: defaultCode,
      message: error.message,
      details: {
        name: error.name,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID(),
      severity: 'high',
    };
  } else {
    adminError = {
      code: defaultCode,
      message: 'An unexpected admin error occurred',
      details: { error: String(error) },
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID(),
      severity: 'high',
    };
  }
  
  return {
    success: false,
    error: adminError,
  };
}

// ========================================
// ADMIN VALIDATION HELPERS
// ========================================

/**
 * Validate admin email format
 */
export function isValidAdminEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValidFormat = emailRegex.test(email);
  
  // Additional admin email validation (e.g., must be from specific domains)
  const allowedDomains = process.env.ADMIN_EMAIL_DOMAINS?.split(',') || [];
  if (allowedDomains.length > 0) {
    const domain = email.split('@')[1]?.toLowerCase();
    return isValidFormat && allowedDomains.includes(domain);
  }
  
  return isValidFormat;
}

/**
 * Validate admin role
 */
export function isValidAdminRole(role: string): boolean {
  const validRoles = [
    'super_admin',
    'admin',
    'moderator',
    'support',
    'viewer',
  ];
  
  return validRoles.includes(role);
}

/**
 * Validate admin permission
 */
export function isValidAdminPermission(permission: string): boolean {
  const validPermissions = [
    'users.create',
    'users.read',
    'users.update',
    'users.delete',
    'users.ban',
    'users.unban',
    'admins.create',
    'admins.read',
    'admins.update',
    'admins.delete',
    'admins.assign_roles',
    'admins.revoke_roles',
    'audit_logs.read',
    'audit_logs.export',
    'system.read',
    'system.update',
    'notifications.send',
    'reports.generate',
    'reports.export',
  ];
  
  return validPermissions.includes(permission);
}

// ========================================
// ADMIN SECURITY HELPERS
// ========================================

/**
 * Generate a secure admin token
 */
export function generateAdminToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Hash sensitive admin data
 */
export async function hashAdminData(input: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * Validate admin session
 */
export function isValidAdminSession(sessionData: {
  adminId: string;
  createdAt: string;
  lastActivity: string;
}): boolean {
  const now = Date.now();
  const sessionAge = now - new Date(sessionData.createdAt).getTime();
  const lastActivity = now - new Date(sessionData.lastActivity).getTime();
  
  // Session expires after 8 hours
  const maxSessionAge = 8 * 60 * 60 * 1000;
  // Session expires after 30 minutes of inactivity
  const maxInactivity = 30 * 60 * 1000;
  
  return sessionAge < maxSessionAge && lastActivity < maxInactivity;
}

// ========================================
// ADMIN DATA HELPERS
// ========================================

/**
 * Parse admin JSON safely
 */
export function safeAdminJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return fallback;
  }
}

/**
 * Mask sensitive admin data for logging
 */
export function maskSensitiveAdminData(data: Record<string, unknown>): Record<string, unknown> {
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'email'];
  const masked = { ...data };
  
  for (const [key, value] of Object.entries(masked)) {
    if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
      if (typeof value === 'string' && value.length > 0) {
        masked[key] = value.substring(0, 2) + '*'.repeat(Math.max(0, value.length - 4)) + value.substring(value.length - 2);
      } else {
        masked[key] = '***';
      }
    }
  }
  
  return masked;
}
