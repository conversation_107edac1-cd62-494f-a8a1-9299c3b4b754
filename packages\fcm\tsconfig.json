{"extends": "@encreasl/typescript-config/base.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ES2020"], "target": "ES2020", "module": "ESNext", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/client/*": ["client/*"], "@/server/*": ["server/*"], "@/utils/*": ["utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}