/**
 * @file apps/web/src/server/index.ts
 * @description Server-side exports for the Web App
 * 
 * This file serves as the main entry point for all server-side functionality
 * specific to the web application. It re-exports server actions, utilities,
 * and services that are used throughout the web app.
 */

import 'server-only';

// ========================================
// SERVER ACTIONS
// ========================================

// Contact form server actions
export {
  submitContactForm,
  validateContactFormData,
  type ContactFormSubmission,
} from './actions/contact-actions';

// Newsletter server actions
export {
  subscribeToNewsletter,
  unsubscribeFromNewsletter,
  validateNewsletterEmail,
  type NewsletterSubscription,
} from './actions/newsletter-actions';

// Analytics server actions
export {
  trackPageView,
  trackEvent,
  trackConversion,
  type AnalyticsEvent,
} from './actions/analytics-actions';

// ========================================
// SERVER UTILITIES
// ========================================

// Server-side utilities
export {
  validateServerRequest,
  sanitizeUserInput,
  formatServerResponse,
  handleServerError,
} from './utils/server-utils';

// Rate limiting utilities
export {
  checkRateLimit,
  createRateLimiter,
  type RateLimitConfig,
} from './utils/rate-limit';

// ========================================
// SERVER SERVICES
// ========================================

// Lead qualification service (web-specific business logic)
export {
  LeadQualificationService,
  type LeadScore,
  type LeadQualificationCriteria,
} from './services/lead-qualification-service';

// Marketing analytics service (web-specific analytics)
export {
  MarketingAnalyticsService,
  type ConversionEvent,
  type MarketingAttribution,
  type ConversionFunnel,
} from './services/marketing-analytics-service';

// ========================================
// SERVER UTILITIES
// ========================================

// SEO utilities (app-specific metadata generation)
export {
  generateWebMetadata,
  generateWebStructuredData,
  generateWebBreadcrumbs,
  type WebSEOMetadata,
} from './utils/seo-utils';

// ========================================
// TYPE EXPORTS
// ========================================

// Re-export common server types
export type {
  ServerActionResult,
  ServerError,
  ValidationError,
} from './types/server-types';
