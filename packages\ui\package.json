{"name": "@encreasl/ui", "version": "0.1.0", "private": true, "exports": {"./button": "./src/button.tsx", "./card": "./src/card.tsx", "./table": "./src/table.tsx", "./sidebar": "./src/sidebar.tsx", "./stats-card": "./src/stats-card.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit"}, "devDependencies": {"@encreasl/eslint-config": "workspace:*", "@encreasl/typescript-config": "workspace:*", "@eslint/js": "^9", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "typescript": "^5"}, "peerDependencies": {"react": "^19.1.0", "@types/react": "^19"}}