/**
 * @file packages/fcm/src/client/utils.ts
 * @description FCM client utilities
 */
/**
 * Check if FCM is supported in the current browser
 */
export declare function isFCMSupported(): boolean;
/**
 * Request notification permission
 */
export declare function requestNotificationPermission(): Promise<NotificationPermission>;
/**
 * Show a local notification
 */
export declare function showNotification(title: string, options?: NotificationOptions): void;
/**
 * Format notification data for display
 */
export declare function formatNotificationData(data: Record<string, string>): Record<string, unknown>;
/**
 * Log FCM client event
 */
export declare function logFCMEvent(event: string, data?: Record<string, unknown>): void;
//# sourceMappingURL=utils.d.ts.map