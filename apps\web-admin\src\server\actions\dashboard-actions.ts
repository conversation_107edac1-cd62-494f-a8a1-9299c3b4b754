/**
 * @file apps/web-admin/src/server/actions/dashboard-actions.ts
 * @description Server actions for dashboard analytics and statistics
 */

'use server';

import 'server-only';
import { getCurrentAdminUser } from '@encreasl/auth/server';
import type {
  AdminServerActionResult,
  DashboardStats
} from '../types/admin-server-types';
import { validateAdminRequest, handleAdminError } from '../utils/admin-utils';
import { hasPermission } from '../utils/permissions';

// Re-export types for convenience
export type { DashboardStats } from '../types/admin-server-types';

// ========================================
// DASHBOARD ACTIONS
// ========================================

/**
 * Get dashboard statistics
 */
export async function getDashboardStats(): Promise<AdminServerActionResult<DashboardStats>> {
  try {
    // Validate admin permissions
    await getCurrentAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'dashboard', 'read')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to view dashboard statistics',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Mock dashboard statistics - in production, this would query your database
    const stats: DashboardStats = {
      users: {
        total: 15420,
        active: 12350,
        inactive: 2870,
        banned: 200,
        newThisMonth: 1250,
      },
      admins: {
        total: 8,
        active: 6,
        lastLoginWithin24h: 4,
      },
      system: {
        uptime: 99.8,
        memoryUsage: 68.5,
        cpuUsage: 23.2,
        diskUsage: 45.7,
      },
      activity: {
        totalActions: 45230,
        actionsToday: 1250,
        errorRate: 0.02,
      },
    };

    return {
      success: true,
      data: stats,
      message: 'Dashboard statistics retrieved successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'dashboard_stats_retrieved',
      },
    };

  } catch (error) {
    return handleAdminError(error, 'DASHBOARD_STATS_ERROR');
  }
}
