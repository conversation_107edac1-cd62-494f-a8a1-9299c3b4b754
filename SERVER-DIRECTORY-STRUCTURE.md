# Server Directory Structure

This document outlines the server directory structure implemented in both Next.js apps (`apps/web` and `apps/web-admin`) to organize server-side code effectively.

## 📁 Directory Structure

```
apps/
├── web/
│   └── src/
│       └── server/                    # Web app server-side code
│           ├── index.ts               # Main server exports
│           ├── types/
│           │   └── server-types.ts    # Server type definitions
│           ├── actions/               # Server actions (orchestration)
│           │   ├── contact-actions.ts
│           │   ├── newsletter-actions.ts
│           │   └── analytics-actions.ts
│           ├── services/              # App-specific business logic
│           │   ├── lead-qualification-service.ts
│           │   └── marketing-analytics-service.ts
│           └── utils/                 # App-specific utilities
│               ├── server-utils.ts
│               ├── rate-limit.ts
│               └── seo-utils.ts       # Web-specific SEO helpers
│
└── web-admin/
    └── src/
        └── server/                    # Admin app server-side code
            ├── index.ts               # Main admin server exports
            ├── types/
            │   └── admin-server-types.ts
            ├── actions/               # Admin server actions (orchestration)
            │   ├── user-management-actions.ts
            │   ├── admin-actions.ts
            │   └── audit-actions.ts
            ├── services/              # Admin-specific business logic
            │   ├── user-risk-assessment-service.ts
            │   └── compliance-reporting-service.ts
            └── utils/                 # Admin-specific utilities
                ├── admin-utils.ts
                ├── permissions.ts
                └── export-utils.ts    # Admin export helpers
```

## 🎯 Purpose and Benefits

### **Why Server Directories with Services?**

1. **App-Specific Business Logic**: Each app has unique business requirements that shouldn't be shared
2. **Clear Separation**: Distinguishes between infrastructure (Firebase Functions), utilities (shared packages), and business logic (app services)
3. **Scalability**: Provides structure for growing app-specific functionality
4. **Maintainability**: Keeps related business logic together and easy to find

### **Architecture Layers**

- **Firebase Functions** (`functions/`): Infrastructure services (email sending, background processing)
- **Shared Packages** (`packages/`): Common utilities (auth, FCM, environment validation)
- **App Services**: App-specific business logic and algorithms
- **App Actions**: Orchestration that coordinates shared packages, services, and Firebase Functions

## 📋 Web App Server Structure

### **`apps/web/src/server/`**

**Purpose**: Server-side functionality specific to the marketing website.

#### **Actions** (`actions/`)
- `contact-actions.ts`: Contact form validation, lead qualification orchestration
- `newsletter-actions.ts`: Newsletter subscription validation and database coordination
- `analytics-actions.ts`: Marketing analytics event validation and tracking

#### **Services** (`services/`)
- `lead-qualification-service.ts`: **Web-specific lead scoring and qualification algorithms**
- `marketing-analytics-service.ts`: **Marketing conversion tracking and attribution analysis**

#### **Utils** (`utils/`)
- `server-utils.ts`: Common server utilities and validation helpers
- `rate-limit.ts`: Rate limiting for server actions
- `seo-utils.ts`: Web-specific SEO metadata generation

## 📋 Admin App Server Structure

### **`apps/web-admin/src/server/`**

**Purpose**: Server-side functionality specific to the admin dashboard.

#### **Actions** (`actions/`)
- `user-management-actions.ts`: User CRUD orchestration with risk assessment integration
- `admin-actions.ts`: Admin user management with role validation
- `audit-actions.ts`: Audit logging and compliance report coordination

#### **Services** (`services/`)
- `user-risk-assessment-service.ts`: **Admin-specific user risk analysis and security monitoring**
- `compliance-reporting-service.ts`: **Compliance reporting, audit trails, and regulatory compliance**

#### **Utils** (`utils/`)
- `admin-utils.ts`: Admin-specific utilities and validation helpers
- `permissions.ts`: Permission and role management system
- `export-utils.ts`: Data export formatting and file generation

## 🔑 **Key Distinction: App Services vs Shared Packages**

### **Web App Services (Marketing-Focused)**
```typescript
// Lead qualification with marketing-specific scoring
export class LeadQualificationService {
  static async qualifyLead(contactData: ContactFormData): Promise<LeadScore> {
    // Marketing-specific lead scoring algorithm
    // Different from admin user risk assessment
  }
}

// Marketing attribution and conversion tracking
export class MarketingAnalyticsService {
  static async trackConversion(event: ConversionEvent): Promise<void> {
    // Web-specific conversion tracking
    // Different from admin system analytics
  }
}
```

### **Admin App Services (Security & Compliance-Focused)**
```typescript
// User risk assessment with security focus
export class UserRiskAssessmentService {
  static async assessUserRisk(userId: string): Promise<RiskAssessment> {
    // Admin-specific security risk evaluation
    // Different from marketing lead qualification
  }
}

// Compliance reporting for regulatory requirements
export class ComplianceReportingService {
  static async generateComplianceReport(type: string): Promise<ComplianceReport> {
    // Admin-specific compliance reporting
    // Not needed in marketing web app
  }
}
```

## 🚀 Usage Examples

### **Web App: Lead Qualification Integration**

```typescript
// apps/web/src/server/actions/contact-actions.ts
import { LeadQualificationService } from '../services/lead-qualification-service';

export async function submitContactForm(formData: ContactFormData) {
  // 1. Validate form (orchestration)
  const validatedData = validateContactFormData(formData);
  
  // 2. Qualify lead (app-specific business logic)
  const leadScore = await LeadQualificationService.qualifyLead(validatedData);
  
  // 3. Save to Firestore (triggers Firebase Functions for email)
  await saveContactSubmission({ ...validatedData, leadScore });
  
  // 4. Send immediate notification (shared FCM)
  await sendToTopic('contact_submissions', notification);
}
```

### **Admin App: Risk Assessment Integration**

```typescript
// apps/web-admin/src/server/actions/user-management-actions.ts
import { UserRiskAssessmentService } from '../services/user-risk-assessment-service';

export async function createUser(userData: UserManagementData) {
  // 1. Validate admin permissions (orchestration)
  const admin = await verifyAdminUser();
  
  // 2. Create user (orchestration)
  const newUser = await createUserRecord(userData);
  
  // 3. Assess risk (admin-specific business logic)
  const riskAssessment = await UserRiskAssessmentService.assessUserRisk(newUser.id);
  
  // 4. Log action and send notifications (shared packages)
  await logAdminAction('user_created', newUser.id);
  await sendToTopic('admin_notifications', notification);
}
```

## 🏗️ **Corrected Architecture**

```mermaid
graph TB
    subgraph "App Services (Business Logic)"
        WEB_SERVICES[Web Services<br/>• Lead qualification<br/>• Marketing analytics<br/>• Conversion tracking]
        ADMIN_SERVICES[Admin Services<br/>• Risk assessment<br/>• Compliance reporting<br/>• Security monitoring]
    end
    
    subgraph "App Actions (Orchestration)"
        WEB_ACTIONS[Web Actions<br/>• Form validation<br/>• Data coordination]
        ADMIN_ACTIONS[Admin Actions<br/>• Permission checks<br/>• Audit coordination]
    end
    
    subgraph "Shared Packages (Utilities)"
        AUTH[auth/server<br/>• Token verification]
        FCM[fcm/server<br/>• Message sending]
        ENV[env<br/>• Config validation]
    end
    
    subgraph "Firebase Functions (Infrastructure)"
        EMAIL_FN[Email Functions<br/>• Template rendering<br/>• SMTP delivery]
        NOTIFICATION_FN[Notification Functions<br/>• Background processing]
    end
    
    WEB_ACTIONS --> WEB_SERVICES
    ADMIN_ACTIONS --> ADMIN_SERVICES
    
    WEB_SERVICES --> AUTH
    WEB_SERVICES --> FCM
    ADMIN_SERVICES --> AUTH
    ADMIN_SERVICES --> FCM
    
    WEB_ACTIONS -.->|Triggers| EMAIL_FN
    ADMIN_ACTIONS -.->|Triggers| NOTIFICATION_FN
```

## 🔧 Development Guidelines

### **When to Create App Services**
- **Different algorithms**: Lead scoring vs risk assessment
- **Different data models**: Marketing attribution vs compliance reporting  
- **Different business rules**: Web conversion tracking vs admin security monitoring
- **App-specific workflows**: Marketing funnel vs compliance audit trail

### **When to Use Shared Packages**
- **Common utilities**: Authentication, messaging, environment validation
- **Infrastructure concerns**: Database connections, external API clients
- **Cross-app functionality**: Logging, monitoring, error handling

## ✅ **Final Structure Benefits**

1. **Clear Boundaries**: Infrastructure vs utilities vs business logic
2. **App Autonomy**: Each app can evolve its business logic independently  
3. **Code Reuse**: Common utilities shared via packages
4. **Maintainability**: Related business logic grouped together
5. **Scalability**: Easy to add new app-specific services as needs grow

This structure provides the best of both worlds: shared infrastructure and utilities through packages and Firebase Functions, while maintaining app-specific business logic where it belongs - in dedicated app services.
