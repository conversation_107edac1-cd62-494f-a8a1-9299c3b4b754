# 🚀 Supabase Setup Guide for PayloadCMS

## Step 1: Create Supabase Project

1. **Go to [Supabase Dashboard](https://supabase.com/dashboard)**
2. **Click "New Project"**
3. **Fill in details:**
   - Name: `encreasl-cms`
   - Database Password: Generate a strong password (SAVE THIS!)
   - Region: Choose closest to your location
4. **Click "Create new project"**
5. **Wait 2-3 minutes** for provisioning

## Step 2: Get Connection String

1. **In your Supabase project dashboard:**
   - Go to **Settings** → **Database**
   - Scroll to **Connection info**
   - Copy the **Connection string** (URI format)

2. **Your connection string looks like:**
   ```
   postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
   ```

## Step 3: Update Environment Variables

1. **Open `apps/cms/.env`**
2. **Replace the DATABASE_URI line with your actual connection string:**
   ```bash
   DATABASE_URI=************************************************************************/postgres
   ```

## Step 4: Test Connection

```bash
cd apps/cms
pnpm run db:test
```

**Expected output:**
```
🔄 Testing database connection...
✅ Successfully connected to Supabase!
📊 PostgreSQL version: PostgreSQL 15.x...
🔌 Connection closed
```

## Step 5: Start PayloadCMS

```bash
# From monorepo root
pnpm run dev:cms

# OR from cms directory
cd apps/cms
pnpm run dev
```

## Step 6: Create Admin User

1. **Open http://localhost:3001/admin**
2. **Fill in the admin user form:**
   - Email: <EMAIL>
   - Password: Choose a secure password
   - Confirm password
3. **Click "Create"**

## Step 7: Verify Setup

✅ **Admin panel loads at http://localhost:3001/admin**
✅ **You can log in with your admin credentials**
✅ **Collections are visible: Users, Media, Posts, Services**
✅ **You can create test content**

## Troubleshooting

### Connection Refused Error
```
Error: connect ECONNREFUSED
```
**Solution:** Check your DATABASE_URI in `.env` file

### Authentication Failed
```
Error: password authentication failed
```
**Solution:** Verify your password in the connection string

### Database Not Found
```
Error: database "postgres" does not exist
```
**Solution:** Make sure you're using the correct database name (should be "postgres")

### SSL Connection Issues
If you get SSL errors, try adding `?sslmode=require` to your connection string:
```bash
DATABASE_URI=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require
```

## Next Steps

Once connected:
1. **Create your first blog post**
2. **Add a service offering**
3. **Upload media files**
4. **Test the API endpoints**
5. **Integrate with your frontend apps**

## API Endpoints

- **REST API:** http://localhost:3001/api/
- **GraphQL:** http://localhost:3001/api/graphql
- **Admin Panel:** http://localhost:3001/admin

## Production Deployment

For Vercel deployment, add these environment variables:
- `DATABASE_URI` (your Supabase connection string)
- `PAYLOAD_SECRET` (32-character secret key)
- `NEXT_PUBLIC_SERVER_URL` (your production URL)
