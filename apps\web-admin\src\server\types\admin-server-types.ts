/**
 * @file apps/web-admin/src/server/types/admin-server-types.ts
 * @description Server-side type definitions for the Web Admin App
 */

import 'server-only';

// ========================================
// ADMIN SERVER TYPES
// ========================================

/**
 * Admin server action result type
 */
export type AdminServerActionResult<T = unknown> = {
  success: boolean;
  data?: T;
  error?: AdminServerError;
  message?: string;
  metadata?: {
    timestamp: string;
    adminId: string;
    action: string;
    resourceId?: string;
  };
};

/**
 * Admin server error type with detailed information
 */
export type AdminServerError = {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
  requestId?: string;
  adminId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
};

/**
 * Admin validation error type
 */
export type AdminValidationError = {
  field: string;
  message: string;
  code: string;
  value?: unknown;
};

/**
 * Admin request context type
 */
export type AdminRequestContext = {
  adminId: string;
  sessionId: string;
  permissions: string[];
  roles: string[];
  userAgent?: string;
  ipAddress?: string;
  timestamp: string;
  requestId: string;
};

/**
 * Permission definition
 */
export type Permission = {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  conditions?: Record<string, unknown>;
};

/**
 * Resource access definition
 */
export type ResourceAccess = {
  resource: string;
  actions: string[];
  conditions?: Record<string, unknown>;
};

/**
 * User management data structure
 */
export type UserManagementData = {
  id?: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  status: 'active' | 'inactive' | 'banned' | 'pending';
  permissions?: string[];
  metadata?: Record<string, unknown>;
  createdAt?: string;
  updatedAt?: string;
};

/**
 * Admin user data structure
 */
export type AdminUserData = {
  id?: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  permissions: string[];
  status: 'active' | 'inactive' | 'suspended';
  lastLoginAt?: string;
  createdAt?: string;
  updatedAt?: string;
};

/**
 * Audit log entry structure
 */
export type AuditLogEntry = {
  id: string;
  adminId: string;
  adminEmail: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
  severity: 'info' | 'warning' | 'error';
};

/**
 * Dashboard statistics structure
 */
export type DashboardStats = {
  users: {
    total: number;
    active: number;
    inactive: number;
    banned: number;
    newThisMonth: number;
  };
  admins: {
    total: number;
    active: number;
    lastLoginWithin24h: number;
  };
  system: {
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
  };
  activity: {
    totalActions: number;
    actionsToday: number;
    errorRate: number;
  };
};

/**
 * Analytics filters structure
 */
export type AnalyticsFilters = {
  startDate?: string;
  endDate?: string;
  adminId?: string;
  action?: string;
  resource?: string;
  severity?: string;
  limit?: number;
  offset?: number;
};

/**
 * Export options structure
 */
export type ExportOptions = {
  format: 'csv' | 'json' | 'xlsx';
  filters?: AnalyticsFilters;
  includeMetadata?: boolean;
  compression?: boolean;
};

/**
 * Notification data structure
 */
export type AdminNotification = {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  recipients: string[];
  channels: ('email' | 'fcm' | 'sms')[];
  data?: Record<string, unknown>;
  scheduledAt?: string;
  sentAt?: string;
  status: 'pending' | 'sent' | 'failed';
};
