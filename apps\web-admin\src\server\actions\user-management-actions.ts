/**
 * @file apps/web-admin/src/server/actions/user-management-actions.ts
 * @description Server actions for user management in the admin app
 */

'use server';

import 'server-only';
import { z } from 'zod';
import { getCurrentAdminUser } from '@encreasl/auth/server';
import { sendToTopic } from '@encreasl/fcm/server';
import type {
  AdminServerActionResult,
  UserManagementData
} from '../types/admin-server-types';
import { validateAdminRequest, handleAdminError } from '../utils/admin-utils';
import { hasPermission } from '../utils/permissions';
import { logAdminAction } from './audit-actions';
import { UserRiskAssessmentService } from '../services/user-risk-assessment-service';

// ========================================
// VALIDATION SCHEMAS
// ========================================

const UserDataSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  role: z.enum(['user', 'premium', 'enterprise']),
  status: z.enum(['active', 'inactive', 'banned', 'pending']).default('active'),
  permissions: z.array(z.string()).optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
});

const UserListFiltersSchema = z.object({
  search: z.string().optional(),
  role: z.string().optional(),
  status: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  sortBy: z.enum(['email', 'firstName', 'lastName', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type UserListFilters = Partial<z.infer<typeof UserListFiltersSchema>>;

// Re-export types for convenience
export type { UserManagementData } from '../types/admin-server-types';

// ========================================
// USER MANAGEMENT ACTIONS
// ========================================

/**
 * Create a new user
 */
export async function createUser(
  userData: Omit<UserManagementData, 'id' | 'createdAt' | 'updatedAt'>
): Promise<AdminServerActionResult<UserManagementData>> {
  try {
    // Validate admin permissions
    const adminUser = await getCurrentAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'users', 'create')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to create users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Validate user data
    const validatedData = UserDataSchema.parse(userData);
    
    // Create user record
    const newUser: UserManagementData = {
      id: crypto.randomUUID(),
      ...validatedData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Here you would typically save to your database
    console.log('Creating user:', newUser);

    // Perform initial risk assessment for new user
    const riskAssessment = await UserRiskAssessmentService.assessUserRisk(newUser.id!);
    console.log('Initial risk assessment:', riskAssessment);

    // Log admin action
    await logAdminAction({
      action: 'user_created',
      resource: 'user',
      resourceId: newUser.id,
      details: {
        userEmail: newUser.email,
        userRole: newUser.role,
      },
      severity: 'info',
    });

    // Send notification
    await sendToTopic('admin_notifications', {
      title: 'User Created',
      body: `New user ${newUser.email} was created by ${adminUser.email || 'system'}`,
      data: {
        type: 'user_created',
        userId: newUser.id!,
        adminId: context.adminId,
      },
    });

    return {
      success: true,
      data: newUser,
      message: 'User created successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'user_created',
        resourceId: newUser.id,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'USER_CREATION_ERROR');
  }
}

/**
 * Update an existing user
 */
export async function updateUser(
  userId: string,
  userData: Partial<Omit<UserManagementData, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<AdminServerActionResult<UserManagementData>> {
  try {
    // Validate admin permissions
    await getCurrentAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'users', 'update')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to update users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Validate user data (partial)
    const validatedData = UserDataSchema.partial().parse(userData);
    
    // Update user record
    const updatedUser: UserManagementData = {
      id: userId,
      email: '<EMAIL>', // This would come from database
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      status: 'active',
      ...validatedData,
      updatedAt: new Date().toISOString(),
    };

    // Here you would typically update in your database
    console.log('Updating user:', updatedUser);

    // Log admin action
    await logAdminAction({
      action: 'user_updated',
      resource: 'user',
      resourceId: userId,
      details: {
        updatedFields: Object.keys(validatedData),
        changes: validatedData,
      },
      severity: 'info',
    });

    return {
      success: true,
      data: updatedUser,
      message: 'User updated successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'user_updated',
        resourceId: userId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'USER_UPDATE_ERROR');
  }
}

/**
 * Delete a user
 */
export async function deleteUser(
  userId: string
): Promise<AdminServerActionResult<{ userId: string; deletedAt: string }>> {
  try {
    // Validate admin permissions
    const adminUser = await getCurrentAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'users', 'delete')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to delete users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'high',
        },
      };
    }

    // Here you would typically delete from your database
    console.log('Deleting user:', userId);

    const deletionResult = {
      userId,
      deletedAt: new Date().toISOString(),
    };

    // Log admin action
    await logAdminAction({
      action: 'user_deleted',
      resource: 'user',
      resourceId: userId,
      details: {
        deletedAt: deletionResult.deletedAt,
      },
      severity: 'warning',
    });

    // Send critical notification
    await sendToTopic('admin_notifications', {
      title: 'User Deleted',
      body: `User ${userId} was deleted by ${adminUser.email || 'system'}`,
      data: {
        type: 'user_deleted',
        userId,
        adminId: context.adminId,
        severity: 'high',
      },
    });

    return {
      success: true,
      data: deletionResult,
      message: 'User deleted successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'user_deleted',
        resourceId: userId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'USER_DELETION_ERROR');
  }
}

/**
 * Get user by ID
 */
export async function getUserById(
  userId: string
): Promise<AdminServerActionResult<UserManagementData>> {
  try {
    // Validate admin permissions
    await getCurrentAdminUser();
    const context = await validateAdminRequest();

    if (!hasPermission(context.permissions, 'users', 'read')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to view users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'low',
        },
      };
    }

    // Here you would typically fetch from your database
    const user: UserManagementData = {
      id: userId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return {
      success: true,
      data: user,
      message: 'User retrieved successfully',
    };

  } catch (error) {
    return handleAdminError(error, 'USER_RETRIEVAL_ERROR');
  }
}

/**
 * Get users list with filters
 */
export async function getUsersList(
  filters: UserListFilters = {}
): Promise<AdminServerActionResult<{
  users: UserManagementData[];
  total: number;
  hasMore: boolean;
}>> {
  try {
    // Validate admin permissions
    await getCurrentAdminUser();
    const context = await validateAdminRequest();

    if (!hasPermission(context.permissions, 'users', 'read')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to view users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'low',
        },
      };
    }

    // Validate filters
    UserListFiltersSchema.parse(filters);

    // Here you would typically query your database
    const mockUsers: UserManagementData[] = [
      {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'premium',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    const result = {
      users: mockUsers,
      total: mockUsers.length,
      hasMore: false,
    };

    return {
      success: true,
      data: result,
      message: 'Users retrieved successfully',
    };

  } catch (error) {
    return handleAdminError(error, 'USERS_LIST_ERROR');
  }
}

/**
 * Ban a user
 */
export async function banUser(
  userId: string,
  reason: string
): Promise<AdminServerActionResult<UserManagementData>> {
  try {
    // Validate admin permissions
    await getCurrentAdminUser();
    const context = await validateAdminRequest();

    if (!hasPermission(context.permissions, 'users', 'ban')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to ban users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'high',
        },
      };
    }

    // Update user status to banned
    const bannedUser: UserManagementData = {
      id: userId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      status: 'banned',
      metadata: { banReason: reason, bannedBy: context.adminId },
      updatedAt: new Date().toISOString(),
    };

    // Log admin action
    await logAdminAction({
      action: 'user_banned',
      resource: 'user',
      resourceId: userId,
      details: {
        reason,
        bannedBy: context.adminId,
      },
      severity: 'warning',
    });

    return {
      success: true,
      data: bannedUser,
      message: 'User banned successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'user_banned',
        resourceId: userId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'USER_BAN_ERROR');
  }
}

/**
 * Unban a user
 */
export async function unbanUser(
  userId: string
): Promise<AdminServerActionResult<UserManagementData>> {
  try {
    // Validate admin permissions
    await getCurrentAdminUser();
    const context = await validateAdminRequest();

    if (!hasPermission(context.permissions, 'users', 'unban')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to unban users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Update user status to active
    const unbannedUser: UserManagementData = {
      id: userId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      status: 'active',
      metadata: { unbannedBy: context.adminId },
      updatedAt: new Date().toISOString(),
    };

    // Log admin action
    await logAdminAction({
      action: 'user_unbanned',
      resource: 'user',
      resourceId: userId,
      details: {
        unbannedBy: context.adminId,
      },
      severity: 'info',
    });

    return {
      success: true,
      data: unbannedUser,
      message: 'User unbanned successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'user_unbanned',
        resourceId: userId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'USER_UNBAN_ERROR');
  }
}
