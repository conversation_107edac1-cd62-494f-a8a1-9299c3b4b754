# 🏗️ Encreasl Monorepo Structure - Complete

encreasl/                                    # 🏠 Monorepo Root
├── 📄 package.json                          # 🏠 Root package configuration
├── 📄 package-lock.json                     # NPM lock file
├── 📄 pnpm-lock.yaml                        # PNPM lock file
├── 📄 pnpm-workspace.yaml                   # 📦 PNPM workspace config
├── 📄 turbo.json                            # ⚡ Turborepo configuration
├── 📄 vercel.json                           # 🚀 Vercel deployment config
├── 📄 firebase.json                         # 🔥 Firebase project config
├── 📄 .env.example                          # 🌍 Environment template
├── 📄 .gitignore                            # 🚫 Git ignore rules
├── 📄 README.md                             # 📖 Main documentation


## 📦 Packages Directory Structure

packages/                                    # 📦 Shared Packages
├── 📁 auth/                                 # 🔐 Authentication Package
│   ├── 📁 src/
│   │   ├── 📄 auth-service.ts               # Core authentication service
│   │   ├── 📄 firebase-admin.ts             # Server-side Firebase Admin auth
│   │   ├── 📄 firebase-config.ts            # Firebase client configuration
│   │   ├── 📄 hooks.ts                      # React authentication hooks
│   │   ├── 📄 types.ts                      # Authentication type definitions
│   │   ├── 📄 index.ts                      # Client-side exports
│   │   └── 📁 server/                       # 🖥️ Server-only exports
│   │       └── 📄 index.ts                  # Server-side auth exports
│   ├── 📄 server.ts                         # Server entry point
│   ├── 📄 package.json                      # Package configuration
│   ├── 📄 tsconfig.json                     # TypeScript configuration
│   └── 📁 node_modules/                     # Dependencies
├── 📁 env/                                  # 🌍 Environment Management
│   ├── 📁 src/
│   │   └── 📄 index.ts                      # Environment validation & exports
│   ├── 📄 package.json                      # Package configuration
│   ├── 📄 tsconfig.json                     # TypeScript configuration
│   └── 📁 node_modules/                     # Dependencies
├── 📁 fcm/                                  # 📱 Firebase Cloud Messaging
│   ├── 📁 src/
│   │   ├── 📁 client/                       # 📱 Client-side FCM utilities
│   │   │   ├── 📄 hooks.ts                  # React FCM hooks (useFCM)
│   │   │   └── 📄 index.ts                  # Client exports
│   │   ├── 📁 config/                       # ⚙️ FCM Configuration
│   │   │   └── 📄 index.ts                  # Configuration exports
│   │   ├── 📁 server/                       # 🖥️ Server-side FCM utilities
│   │   │   └── 📄 index.ts                  # Server FCM operations
│   │   ├── 📁 types/                        # 📝 FCM Type Definitions
│   │   │   └── 📄 index.ts                  # FCM type exports
│   │   ├── 📁 utils/                        # 🔧 FCM Utilities
│   │   │   └── 📄 index.ts                  # Utility functions
│   │   └── 📄 index.ts                      # Main FCM exports
│   ├── 📁 dist/                             # 📦 Compiled output
│   ├── 📄 package.json                      # Package configuration
│   ├── 📄 tsconfig.json                     # TypeScript configuration
│   └── 📁 node_modules/                     # Dependencies
├── 📁 ui/                                   # 🎨 UI Components Library
│   ├── 📁 src/
│   │   ├── 📄 button.tsx                    # Reusable button component
│   │   ├── 📄 card.tsx                      # Card component
│   │   ├── 📄 sidebar.tsx                   # Sidebar navigation component
│   │   ├── 📄 stats-card.tsx                # Statistics card component
│   │   └── 📄 table.tsx                     # Data table component
│   ├── 📄 package.json                      # Package configuration
│   ├── 📄 tsconfig.json                     # TypeScript configuration
│   ├── 📄 eslint.config.js                  # ESLint configuration
│   └── 📁 node_modules/                     # Dependencies
├── 📁 eslint-config/                        # 🔧 ESLint Configuration
│   ├── 📄 base.js                           # Base ESLint rules
│   ├── 📄 nextjs.js                         # Next.js specific ESLint rules
│   └── 📄 package.json                      # Package configuration
└── 📁 typescript-config/                   # 📘 TypeScript Configuration
    ├── 📄 base.json                         # Base TypeScript configuration
    ├── 📄 nextjs.json                       # Next.js specific TypeScript config
    └── 📄 package.json                      # Package configuration



## 📦 Functions Directory Structure
    functions/                                   # ⚡ Firebase Cloud Functions
├── 📁 src/                                  # 📝 Source Code
│   ├── 📁 admin/                            # 👨‍💼 Admin System
│   │   ├── 📄 adminAuth.ts                  # Admin authentication logic
│   │   ├── 📄 adminFunctions.ts             # Admin CRUD operations
│   │   ├── 📄 adminTypes.ts                 # Admin type definitions
│   │   ├── 📄 createAdminCollections.ts     # Firestore collection setup
│   │   ├── 📄 createJohnAdmin.ts            # Default admin user creation
│   │   ├── 📄 roleManagement.ts             # Role & permission management
│   │   └── 📄 setupAdmin.ts                 # Admin system initialization
│   ├── 📁 services/                         # 🔧 Business Logic Services
│   │   └── 📄 fcm-service.ts                # FCM service implementation
│   ├── 📁 triggers/                         # 🎯 Event Triggers & Functions
│   │   ├── 📄 callable-functions.ts         # Client-callable functions
│   │   ├── 📄 campaign-notifications.ts     # Campaign event handlers
│   │   ├── 📄 contact-notifications.ts      # Contact form event handlers
│   │   ├── 📄 http-notifications.ts         # HTTP API endpoints
│   │   ├── 📄 newsletter-notifications.ts   # Newsletter event handlers
│   │   ├── 📄 pubsub-notifications.ts       # PubSub message processors
│   │   ├── 📄 scheduled-notifications.ts    # Scheduled/cron job functions
│   │   ├── 📄 user-auth-triggers.ts         # User authentication triggers
│   │   └── 📄 user-notifications.ts         # User lifecycle event handlers
│   ├── 📁 types/                            # 📝 Type Definitions
│   │   ├── 📄 notifications.ts              # Notification type definitions
│   │   └── 📄 users.ts                      # User type definitions
│   ├── 📁 users/                            # 👥 User Management
│   │   ├── 📄 userFunctions.ts              # User CRUD operations
│   │   └── 📄 userUtils.ts                  # User utility functions
│   └── 📄 index.ts                          # Main function exports
├── 📁 lib/                                  # 📦 Compiled JavaScript Output
│   ├── 📁 admin/                            # Compiled admin functions
│   ├── 📁 services/                         # Compiled services
│   ├── 📁 triggers/                         # Compiled trigger functions
│   ├── 📁 types/                            # Compiled type definitions
│   ├── 📁 users/                            # Compiled user functions
│   ├── 📄 index.js                          # Compiled main exports
│   ├── 📄 index.js.map                      # Source map
│   ├── 📄 index.d.ts                        # TypeScript declarations
│   └── 📄 index.d.ts.map                    # Declaration source map
├── 📄 package.json                          # Dependencies & scripts
├── 📄 tsconfig.json                         # TypeScript configuration
└── 📁 node_modules/                         # Dependencies
    ├── 📁 @eslint/                          # ESLint packages
    ├── 📁 @firebase/                        # Firebase SDK packages
    ├── 📁 @typescript-eslint/               # TypeScript ESLint packages
    ├── 📁 firebase-admin/                   # Firebase Admin SDK
    └── 📁 [other dependencies]/             # Additional npm packages


## 📦 Apps Directory Structure

├── 📁 apps/                                 # 🚀 Applications
│   ├── 📁 web/                              # 🌐 Marketing Website (Port 3000)
│   │   ├── 📁 src/
│   │   │   ├── 📁 app/                      # Next.js App Router
│   │   │   │   ├── 📁 api/                  # API Routes
│   │   │   │   │   ├── 📁 contact/          # Contact form endpoints
│   │   │   │   │   └── 📁 fcm/              # FCM token management
│   │   │   │   ├── 📄 layout.tsx            # Root layout
│   │   │   │   ├── 📄 page.tsx              # Homepage
│   │   │   │   └── 📄 globals.css           # Global styles
│   │   │   ├── 📁 components/               # React components
│   │   │   │   ├── 📄 ContactForm.tsx       # Contact form component
│   │   │   │   ├── 📄 Hero.tsx              # Hero section
│   │   │   │   └── 📄 Navigation.tsx        # Navigation component
│   │   │   ├── 📁 lib/                      # Client utilities
│   │   │   │   ├── 📄 env.ts                # Environment validation
│   │   │   │   └── 📄 utils.ts              # Helper functions
│   │   │   └── 📁 server/                   # 🖥️ Server-side Code
│   │   │       ├── 📁 actions/              # Server actions (orchestration)
│   │   │       │   ├── 📄 analytics-actions.ts # Analytics tracking
│   │   │       │   ├── 📄 contact-actions.ts   # Contact form handling
│   │   │       │   └── 📄 newsletter-actions.ts # Newsletter management
│   │   │       ├── 📁 utils/                # App-specific utilities
│   │   │       │   ├── 📄 rate-limit.ts     # Rate limiting
│   │   │       │   ├── 📄 seo-utils.ts      # SEO metadata helpers
│   │   │       │   └── 📄 server-utils.ts   # Server utilities
│   │   │       ├── 📁 types/                # Server type definitions
│   │   │       │   └── 📄 server-types.ts   # Server-side types
│   │   │       └── 📄 index.ts              # Server exports
│   │   ├── 📄 next.config.ts                # Next.js configuration
│   │   ├── 📄 tailwind.config.ts            # Tailwind CSS config
│   │   ├── 📄 package.json                  # Dependencies
│   │   └── 📄 .env.example                  # Environment template
│   └── 📁 web-admin/                        # 👨‍💼 Admin Dashboard (Port 3001)
│       ├── 📁 src/
│       │   ├── 📁 app/
│       │   │   ├── 📁 api/                  # Admin API routes
│       │   │   │   ├── 📁 admin/            # Admin management
│       │   │   │   │   ├── 📁 fcm/          # Admin FCM endpoints
│       │   │   │   │   └── 📁 users/        # User management
│       │   │   │   ├── 📁 auth/             # Authentication
│       │   │   │   │   └── 📁 verify-admin/ # Admin verification
│       │   │   │   ├── 📁 roles/            # Role management
│       │   │   │   └── 📁 users/            # User operations
│       │   │   ├── 📁 admin/                # Admin pages
│       │   │   ├── 📁 login/                # Login page
│       │   │   ├── 📄 layout.tsx            # Admin layout
│       │   │   └── 📄 page.tsx              # Dashboard home
│       │   ├── 📁 components/               # Admin components
│       │   │   ├── 📄 AdminDashboard.tsx    # Main dashboard
│       │   │   └── 📁 auth/                 # Auth components
│       │   ├── 📁 contexts/                 # React contexts
│       │   │   └── 📄 AuthContext.tsx       # Authentication context
│       │   ├── 📁 hooks/                    # Custom hooks
│       │   │   └── 📄 useAuth.ts            # Authentication hook
│       │   ├── 📁 lib/                      # Admin utilities
│       │   │   ├── 📄 env.ts                # Environment validation
│       │   │   └── 📄 firebase.ts           # Firebase configuration
│       │   └── 📁 server/                   # 🖥️ Admin Server-side Code
│       │       ├── 📁 actions/              # Admin server actions (orchestration)
│       │       │   ├── 📄 admin-actions.ts  # Admin user management
│       │       │   ├── 📄 audit-actions.ts  # Audit logging & export
│       │       │   └── 📄 user-management-actions.ts # User CRUD operations
│       │       ├── 📁 utils/                # Admin-specific utilities
│       │       │   ├── 📄 admin-utils.ts    # Admin utilities
│       │       │   ├── 📄 export-utils.ts   # Data export helpers
│       │       │   └── 📄 permissions.ts    # Permission system
│       │       ├── 📁 types/                # Admin server type definitions
│       │       │   └── 📄 admin-server-types.ts # Admin server types
│       │       └── 📄 index.ts              # Admin server exports
│       ├── 📄 next.config.ts                # Next.js configuration
│       ├── 📄 package.json                  # Dependencies
│       └── 📄 .env.example                  # Environment template
├── 📁 packages/                             # 📦 Shared Packages
│   ├── 📁 ui/                               # 🎨 UI Components Library
│   │   ├── 📁 src/
│   │   │   ├── 📄 button.tsx                # Button component
│   │   │   ├── 📄 card.tsx                  # Card component
│   │   │   ├── 📄 input.tsx                 # Input component
│   │   │   ├── 📄 table.tsx                 # Table component
│   │   │   ├── 📄 sidebar.tsx               # Sidebar component
│   │   │   ├── 📄 stats-card.tsx            # Stats card component
│   │   │   └── 📄 index.ts                  # Exports
│   │   ├── 📄 package.json                  # Package configuration
│   │   ├── 📄 tsconfig.json                 # TypeScript config
│   │   └── 📄 eslint.config.js              # ESLint config
│   ├── 📁 auth/                             # 🔐 Authentication Package
│   │   ├── 📁 src/
│   │   │   ├── 📄 auth-service.ts           # Core auth service
│   │   │   ├── 📄 firebase-admin.ts         # Server-side auth
│   │   │   ├── 📄 firebase-config.ts        # Firebase configuration
│   │   │   ├── 📄 hooks.ts                  # React hooks
│   │   │   ├── 📄 types.ts                  # Auth types
│   │   │   ├── 📄 utils.ts                  # Auth utilities
│   │   │   ├── 📄 index.ts                  # Client exports
│   │   │   └── 📁 server/                   # Server-only exports
│   │   │       └── 📄 index.ts              # Server exports
│   │   ├── 📄 server.ts                     # Server entry point
│   │   ├── 📄 package.json                  # Package configuration
│   │   └── 📄 tsconfig.json                 # TypeScript config
│   ├── 📁 fcm/                              # 📱 Firebase Cloud Messaging
│   │   ├── 📁 src/
│   │   │   ├── 📁 client/                   # Client-side utilities
│   │   │   │   ├── 📄 hooks.ts              # React hooks (useFCM)
│   │   │   │   ├── 📄 browser.ts            # Browser APIs
│   │   │   │   └── 📄 service-worker.ts     # SW integration
│   │   │   ├── 📁 server/                   # Server-side utilities
│   │   │   │   ├── 📄 fcm-admin.ts          # Admin SDK helpers
│   │   │   │   ├── 📄 index.ts              # Server exports
│   │   │   │   └── 📄 utils.ts              # Server operations
│   │   │   ├── 📁 types/                    # TypeScript definitions
│   │   │   │   ├── 📄 messages.ts           # Message types
│   │   │   │   ├── 📄 devices.ts            # Device types
│   │   │   │   ├── 📄 topics.ts             # Topic types
│   │   │   │   └── 📄 index.ts              # Type exports
│   │   │   ├── 📁 config/                   # Configuration
│   │   │   │   ├── 📄 index.ts              # Config exports
│   │   │   │   └── 📄 topics.ts             # Topic definitions
│   │   │   ├── 📁 utils/                    # Common utilities
│   │   │   │   ├── 📄 validation.ts         # Message validation
│   │   │   │   ├── 📄 formatting.ts         # Message formatting
│   │   │   │   ├── 📄 errors.ts             # Error handling
│   │   │   │   └── 📄 index.ts              # Utils exports
│   │   │   └── 📄 index.ts                  # Main exports
│   │   ├── 📁 dist/                         # Compiled output
│   │   ├── 📄 package.json                  # Package configuration
│   │   └── 📄 tsconfig.json                 # TypeScript config
│   ├── 📁 env/                              # 🌍 Environment Management
│   │   ├── 📁 src/
│   │   │   ├── 📄 shared.ts                 # Shared env variables
│   │   │   ├── 📄 web.ts                    # Web app env
│   │   │   ├── 📄 admin.ts                  # Admin app env
│   │   │   ├── 📄 validation.ts             # Zod schemas
│   │   │   └── 📄 index.ts                  # Exports
│   │   ├── 📄 package.json                  # Package configuration
│   │   └── 📄 tsconfig.json                 # TypeScript config
│   ├── 📁 eslint-config/                    # 🔧 ESLint Configuration
│   │   ├── 📄 base.js                       # Base ESLint config
│   │   ├── 📄 nextjs.js                     # Next.js specific config
│   │   └── 📄 package.json                  # Package configuration
│   └── 📁 typescript-config/               # 📘 TypeScript Configuration
│       ├── 📄 base.json                     # Base TypeScript config
│       ├── 📄 nextjs.json                   # Next.js specific config
│       └── 📄 package.json                  # Package configuration
├── 📁 functions/                            # ⚡ Firebase Cloud Functions
│   ├── 📁 src/
│   │   ├── 📁 admin/                        # 👨‍💼 Admin System
│   │   │   ├── 📄 adminAuth.ts              # Admin authentication
│   │   │   ├── 📄 adminFunctions.ts         # Admin CRUD operations
│   │   │   ├── 📄 adminTypes.ts             # Admin type definitions
│   │   │   ├── 📄 createAdminCollections.ts # Collection setup
│   │   │   ├── 📄 createJohnAdmin.ts        # Default admin creation
│   │   │   ├── 📄 roleManagement.ts         # Role & permission system
│   │   │   └── 📄 setupAdmin.ts             # Admin system initialization
│   │   ├── 📁 triggers/                     # 🎯 Event Triggers
│   │   │   ├── 📄 callable-functions.ts     # Client-callable functions
│   │   │   ├── 📄 campaign-notifications.ts # Campaign event handlers
│   │   │   ├── 📄 contact-notifications.ts  # Contact form handlers
│   │   │   ├── 📄 http-notifications.ts     # HTTP API endpoints
│   │   │   ├── 📄 newsletter-notifications.ts # Newsletter handlers
│   │   │   ├── 📄 pubsub-notifications.ts   # PubSub processors
│   │   │   ├── 📄 scheduled-notifications.ts # Cron jobs
│   │   │   ├── 📄 user-auth-triggers.ts     # Auth event handlers
│   │   │   └── 📄 user-notifications.ts     # User lifecycle handlers
│   │   ├── 📁 services/                     # 🔧 Business Logic Services
│   │   │   └── 📄 fcm-service.ts            # FCM service implementation
│   │   ├── 📁 users/                        # 👥 User Management
│   │   │   ├── 📄 userFunctions.ts          # User CRUD operations
│   │   │   └── 📄 userUtils.ts              # User utilities
│   │   ├── 📁 types/                        # 📝 Type Definitions
│   │   │   ├── 📄 notifications.ts          # Notification types
│   │   │   └── 📄 users.ts                  # User types
│   │   └── 📄 index.ts                      # Function exports
│   ├── 📁 lib/                              # 📦 Compiled JavaScript
│   ├── 📄 package.json                      # Dependencies
│   ├── 📄 tsconfig.json                     # TypeScript configuration
│   └── 📄 .eslintrc.js                      # ESLint configuration
├── 📁 scripts/                              # 🚀 Automation Scripts
│   ├── 📄 create-admin-user.js              # Admin user creation
│   ├── 📄 create-john-admin.js              # Default admin setup
│   ├── 📄 deploy-fcm.js                     # FCM deployment automation
│   ├── 📄 link-john-admin-uid.js            # Admin UID linking
│   ├── 📄 setup-env.js                      # Environment setup
│   ├── 📄 verify-john-admin.js              # Admin verification
│   ├── 📄 verify-setup.js                   # Setup verification
│   ├── 📄 package.json                      # Script dependencies
│   └── 📄 README.md                         # Scripts documentation
├── 📁 docs/                                 # 📚 Documentation
│   ├── 📄 admin-schema.md                   # Admin system schema
│   ├── 📄 admin-system-setup.md             # Admin setup guide
│   ├── 📄 environment-variables.md          # Environment guide
│   ├── 📄 fcm-deployment-guide.md           # FCM deployment
│   ├── 📄 firebase-cloud-messaging.md       # FCM implementation
│   ├── 📄 firebase-principles.md            # Firebase best practices
│   └── 📄 users-schema.md                   # User system schema
└── 📁 templates/                            # 📋 Code Templates
    └── 📄 firebase-messaging-sw-web.js      # Service worker template