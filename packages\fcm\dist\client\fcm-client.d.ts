/**
 * @file packages/fcm/src/client/fcm-client.ts
 * @description Firebase Cloud Messaging client utilities
 */
export type FCMClientMessage = {
    title: string;
    body: string;
    data?: Record<string, string>;
    imageUrl?: string;
    clickAction?: string;
};
export type FCMSubscriptionResult = {
    success: boolean;
    token?: string;
    error?: string;
};
/**
 * Initialize FCM client
 */
export declare function initializeFCM(): Promise<FCMSubscriptionResult>;
/**
 * Get FCM token
 */
export declare function getFCMToken(): Promise<string | null>;
/**
 * Subscribe to topic
 */
export declare function subscribeToTopic(topic: string): Promise<FCMSubscriptionResult>;
/**
 * Unsubscribe from topic
 */
export declare function unsubscribeFromTopic(topic: string): Promise<FCMSubscriptionResult>;
//# sourceMappingURL=fcm-client.d.ts.map