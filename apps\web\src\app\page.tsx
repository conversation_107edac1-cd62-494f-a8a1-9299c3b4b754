"use client";

import { useState } from "react";

// Sidebar Item Component
function SidebarItem({ icon, label, active = false, collapsed = false }: {
  icon: string;
  label: string;
  active?: boolean;
  collapsed?: boolean;
}) {
  const getIcon = (iconName: string) => {
    const iconMap: { [key: string]: JSX.Element } = {
      home: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      shorts: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
        </svg>
      ),
      subscriptions: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      history: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      playlists: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
        </svg>
      ),
      "watch-later": (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      liked: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      trending: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
      music: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
        </svg>
      ),
      gaming: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
        </svg>
      ),
      news: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
        </svg>
      ),
      sports: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    };
    return iconMap[iconName] || iconMap.home;
  };

  return (
    <button
      className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
        active
          ? 'bg-gray-100 text-gray-900'
          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
      }`}
    >
      <div className="flex-shrink-0">
        {getIcon(icon)}
      </div>
      {!collapsed && <span className="ml-3 truncate">{label}</span>}
    </button>
  );
}

// Category Pill Component
function CategoryPill({ label, active = false }: { label: string; active?: boolean }) {
  return (
    <button
      className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
        active
          ? 'bg-gray-900 text-white'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
    >
      {label}
    </button>
  );
}

// Video Card Component
function VideoCard({
  thumbnail,
  title,
  channel,
  views,
  time,
  duration,
}: {
  thumbnail: string;
  title: string;
  channel: string;
  views: string;
  time: string;
  duration: string;
}) {
  return (
    <div className="group cursor-pointer">
      <div className="relative aspect-video bg-gray-200 rounded-lg overflow-hidden mb-3">
        <img
          src={thumbnail}
          alt={title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
        />
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-1.5 py-0.5 rounded">
          {duration}
        </div>
      </div>
      <div className="flex space-x-3">
        <div className="flex-shrink-0">
          <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
            {channel.charAt(0)}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {title}
          </h3>
          <p className="text-sm text-gray-600 mt-1">{channel}</p>
          <p className="text-sm text-gray-600">
            {views} • {time}
          </p>
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-50">
        <div className="flex items-center justify-between px-4 py-2">
          {/* Left section */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </div>
              <span className="text-xl font-semibold">Encreasl</span>
            </div>
          </div>

          {/* Center search */}
          <div className="flex-1 max-w-2xl mx-8">
            <div className="flex">
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full px-4 py-2 border border-gray-300 rounded-l-full focus:outline-none focus:border-blue-500"
                />
              </div>
              <button className="px-6 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-full hover:bg-gray-200">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
              <button className="ml-2 p-2 hover:bg-gray-100 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </button>
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-100 rounded-full">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
              </svg>
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
              </svg>
            </button>
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
              U
            </div>
          </div>
        </div>
      </header>

      <div className="flex pt-16">
        {/* Sidebar */}
        <aside className={`fixed left-0 top-16 h-full bg-white border-r border-gray-200 transition-all duration-300 z-40 ${
          sidebarOpen ? 'w-64' : 'w-16'
        }`}>
          <div className="p-3">
            <nav className="space-y-1">
              {/* Main Navigation */}
              <div className="space-y-1">
                <SidebarItem icon="home" label="Home" active={true} collapsed={!sidebarOpen} />
                <SidebarItem icon="shorts" label="Shorts" collapsed={!sidebarOpen} />
                <SidebarItem icon="subscriptions" label="Subscriptions" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* You section */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">You</div>}
                <SidebarItem icon="history" label="History" collapsed={!sidebarOpen} />
                <SidebarItem icon="playlists" label="Playlists" collapsed={!sidebarOpen} />
                <SidebarItem icon="watch-later" label="Watch later" collapsed={!sidebarOpen} />
                <SidebarItem icon="liked" label="Liked videos" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* Explore section */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Explore</div>}
                <SidebarItem icon="trending" label="Trending" collapsed={!sidebarOpen} />
                <SidebarItem icon="music" label="Music" collapsed={!sidebarOpen} />
                <SidebarItem icon="gaming" label="Gaming" collapsed={!sidebarOpen} />
                <SidebarItem icon="news" label="News" collapsed={!sidebarOpen} />
                <SidebarItem icon="sports" label="Sports" collapsed={!sidebarOpen} />
              </div>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
          {/* Category Pills */}
          <div className="sticky top-16 bg-white border-b border-gray-200 z-30">
            <div className="flex items-center space-x-3 px-6 py-3 overflow-x-auto">
              <CategoryPill label="All" active={true} />
              <CategoryPill label="Business" />
              <CategoryPill label="Technology" />
              <CategoryPill label="Marketing" />
              <CategoryPill label="Analytics" />
              <CategoryPill label="E-commerce" />
              <CategoryPill label="Growth" />
              <CategoryPill label="Strategy" />
              <CategoryPill label="Innovation" />
              <CategoryPill label="Leadership" />
            </div>
          </div>

          {/* Video Grid */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=225&fit=crop"
                title="Enterprise Marketing Strategies for 2024"
                channel="Encreasl Business"
                views="1.2M views"
                time="3 days ago"
                duration="15:42"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=225&fit=crop"
                title="Data Analytics Dashboard Best Practices"
                channel="Tech Insights"
                views="856K views"
                time="1 week ago"
                duration="22:15"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=225&fit=crop"
                title="Building Scalable E-commerce Platforms"
                channel="Development Pro"
                views="2.1M views"
                time="2 weeks ago"
                duration="18:30"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1553877522-43269d4ea984?w=400&h=225&fit=crop"
                title="Customer Retention Strategies That Work"
                channel="Growth Hacker"
                views="743K views"
                time="4 days ago"
                duration="12:08"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=225&fit=crop"
                title="AI in Business: Practical Applications"
                channel="Future Tech"
                views="1.8M views"
                time="1 day ago"
                duration="25:45"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop"
                title="Leadership in Remote Teams"
                channel="Management Today"
                views="924K views"
                time="5 days ago"
                duration="16:22"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=225&fit=crop"
                title="Digital Transformation Success Stories"
                channel="Enterprise Solutions"
                views="1.5M views"
                time="1 week ago"
                duration="20:15"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=400&h=225&fit=crop"
                title="Performance Marketing ROI Optimization"
                channel="Marketing Metrics"
                views="687K views"
                time="3 days ago"
                duration="14:33"
              />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
