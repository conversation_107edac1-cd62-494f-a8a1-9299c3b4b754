"use client";

import { useState } from "react";

// Sidebar Item Component
function SidebarItem({ icon, label, active = false, collapsed = false }: {
  icon: string;
  label: string;
  active?: boolean;
  collapsed?: boolean;
}) {
  const getIcon = (iconName: string) => {
    const iconMap: { [key: string]: JSX.Element } = {
      home: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      shorts: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
        </svg>
      ),
      subscriptions: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      history: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      playlists: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
        </svg>
      ),
      "watch-later": (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      liked: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      trending: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
      music: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
        </svg>
      ),
      gaming: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
        </svg>
      ),
      news: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
        </svg>
      ),
      sports: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    };
    return iconMap[iconName] || iconMap.home;
  };

  return (
    <button
      className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
        active
          ? 'bg-gray-100 text-gray-900'
          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
      }`}
    >
      <div className="flex-shrink-0">
        {getIcon(icon)}
      </div>
      {!collapsed && <span className="ml-3 truncate">{label}</span>}
    </button>
  );
}

// Category Circle Component
function CategoryCircle({ label, active = false }: { label: string; active?: boolean }) {
  const getCategoryIcon = (category: string) => {
    const iconMap: { [key: string]: { icon: JSX.Element; bgColor: string } } = {
      All: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-purple-500 to-purple-700'
      },
      Business: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-blue-500 to-blue-700'
      },
      Technology: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-cyan-500 to-cyan-700'
      },
      Marketing: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-pink-500 to-pink-700'
      },
      Analytics: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-green-500 to-green-700'
      },
      'E-commerce': {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-orange-500 to-orange-700'
      },
      Growth: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-emerald-500 to-emerald-700'
      },
      Strategy: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-indigo-500 to-indigo-700'
      },
      Innovation: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-yellow-500 to-yellow-700'
      },
      Leadership: {
        icon: (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        ),
        bgColor: 'bg-gradient-to-br from-red-500 to-red-700'
      }
    };
    return iconMap[category] || iconMap.All;
  };

  const { icon, bgColor } = getCategoryIcon(label);

  return (
    <div className="flex flex-col items-center space-y-2 min-w-0">
      <button
        className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 ${
          active
            ? `${bgColor} ring-4 ring-blue-200`
            : `${bgColor} hover:shadow-lg`
        }`}
      >
        {icon}
      </button>
      <span className={`text-xs font-medium text-center leading-tight ${
        active ? 'text-blue-600' : 'text-gray-700'
      }`}>
        {label}
      </span>
    </div>
  );
}

// Video Card Component
function VideoCard({
  thumbnail,
  title,
  channel,
  views,
  time,
  duration,
}: {
  thumbnail: string;
  title: string;
  channel: string;
  views: string;
  time: string;
  duration: string;
}) {
  return (
    <div className="group cursor-pointer">
      <div className="relative aspect-video bg-gray-200 rounded-lg overflow-hidden mb-3">
        <img
          src={thumbnail}
          alt={title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
        />
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-1.5 py-0.5 rounded">
          {duration}
        </div>
      </div>
      <div className="flex space-x-3">
        <div className="flex-shrink-0">
          <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
            {channel.charAt(0)}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {title}
          </h3>
          <p className="text-sm text-gray-600 mt-1">{channel}</p>
          <p className="text-sm text-gray-600">
            {views} • {time}
          </p>
        </div>
      </div>
    </div>
  );
}

// Category Carousel Component
function CategoryCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeCategory, setActiveCategory] = useState("All");

  const categories = [
    "All", "Business", "Technology", "Marketing", "Analytics",
    "E-commerce", "Growth", "Strategy", "Innovation", "Leadership"
  ];

  const itemWidth = 140; // 64px circle + 48px gap + padding
  const containerWidth = 5 * itemWidth; // Show about 5 items
  const maxScroll = Math.max(0, (categories.length * itemWidth) - containerWidth);
  const maxIndex = Math.max(0, Math.ceil(maxScroll / itemWidth));

  const scrollLeft = () => {
    setCurrentIndex(Math.max(0, currentIndex - 1));
  };

  const scrollRight = () => {
    setCurrentIndex(Math.min(maxIndex, currentIndex + 1));
  };

  return (
    <div className="relative">
      {/* Left Arrow */}
      {currentIndex > 0 && (
        <button
          onClick={scrollLeft}
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      )}

      {/* Right Arrow */}
      {currentIndex < maxIndex && (
        <button
          onClick={scrollRight}
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      )}

      {/* Carousel Container */}
      <div className="overflow-hidden px-6">
        <div
          className="flex transition-transform duration-300 ease-in-out py-6"
          style={{
            transform: `translateX(-${currentIndex * 140}px)`,
            gap: '48px'
          }}
        >
          {categories.map((category, index) => (
            <div
              key={category}
              className="flex-shrink-0"
            >
              <div
                onClick={() => setActiveCategory(category)}
                className="cursor-pointer"
              >
                <CategoryCircle
                  label={category}
                  active={activeCategory === category}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Dots Indicator */}
      <div className="flex justify-center space-x-2 pb-2">
        {Array.from({ length: maxIndex + 1 }).map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-2 h-2 rounded-full transition-colors ${
              currentIndex === index ? 'bg-blue-500' : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
    </div>
  );
}

export default function Home() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 bg-white border-b border-gray-200 z-50">
        <div className="flex items-center justify-between px-4 py-2">
          {/* Left section */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 hover:bg-gray-100 rounded-full text-gray-800"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </div>
              <span className="text-xl font-semibold text-gray-900">Encreasl</span>
            </div>
          </div>

          {/* Center search */}
          <div className="flex-1 max-w-2xl mx-8">
            <div className="flex">
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full px-4 py-2 border border-gray-300 rounded-l-full focus:outline-none focus:border-blue-500 text-gray-900 placeholder-gray-500"
                />
              </div>
              <button className="px-6 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-full hover:bg-gray-200 text-gray-700">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
              <button className="ml-2 p-2 hover:bg-gray-100 rounded-full text-gray-700">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </button>
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-100 rounded-full text-gray-700">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full text-gray-700">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
              </svg>
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full text-gray-700">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
              </svg>
            </button>
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
              U
            </div>
          </div>
        </div>
      </header>

      {/* Sidebar - Fixed Position */}
      <aside className={`fixed left-0 bg-white border-r border-gray-200 transition-all duration-300 overflow-y-auto z-40 ${
        sidebarOpen ? 'w-64' : 'w-16'
      }`} style={{ height: 'calc(100vh - 4rem)' }}>
          <div className="p-3">
            <nav className="space-y-1">
              {/* Main Navigation */}
              <div className="space-y-1">
                <SidebarItem icon="home" label="Home" active={true} collapsed={!sidebarOpen} />
                <SidebarItem icon="shorts" label="Shorts" collapsed={!sidebarOpen} />
                <SidebarItem icon="subscriptions" label="Subscriptions" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* You section */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">You</div>}
                <SidebarItem icon="history" label="History" collapsed={!sidebarOpen} />
                <SidebarItem icon="playlists" label="Playlists" collapsed={!sidebarOpen} />
                <SidebarItem icon="watch-later" label="Watch later" collapsed={!sidebarOpen} />
                <SidebarItem icon="liked" label="Liked videos" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* Explore section */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Explore</div>}
                <SidebarItem icon="trending" label="Trending" collapsed={!sidebarOpen} />
                <SidebarItem icon="music" label="Music" collapsed={!sidebarOpen} />
                <SidebarItem icon="gaming" label="Gaming" collapsed={!sidebarOpen} />
                <SidebarItem icon="news" label="News" collapsed={!sidebarOpen} />
                <SidebarItem icon="sports" label="Sports" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* Business Tools */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Business Tools</div>}
                <SidebarItem icon="analytics" label="Analytics" collapsed={!sidebarOpen} />
                <SidebarItem icon="dashboard" label="Dashboard" collapsed={!sidebarOpen} />
                <SidebarItem icon="reports" label="Reports" collapsed={!sidebarOpen} />
                <SidebarItem icon="marketing" label="Marketing Hub" collapsed={!sidebarOpen} />
                <SidebarItem icon="ecommerce" label="E-commerce" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* Management */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Management</div>}
                <SidebarItem icon="team" label="Team Management" collapsed={!sidebarOpen} />
                <SidebarItem icon="projects" label="Projects" collapsed={!sidebarOpen} />
                <SidebarItem icon="calendar" label="Calendar" collapsed={!sidebarOpen} />
                <SidebarItem icon="tasks" label="Tasks" collapsed={!sidebarOpen} />
                <SidebarItem icon="workflow" label="Workflow" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* Growth & Strategy */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Growth & Strategy</div>}
                <SidebarItem icon="growth" label="Growth Analytics" collapsed={!sidebarOpen} />
                <SidebarItem icon="strategy" label="Strategy Planning" collapsed={!sidebarOpen} />
                <SidebarItem icon="innovation" label="Innovation Lab" collapsed={!sidebarOpen} />
                <SidebarItem icon="leadership" label="Leadership" collapsed={!sidebarOpen} />
                <SidebarItem icon="consulting" label="Consulting" collapsed={!sidebarOpen} />
              </div>

              {sidebarOpen && <hr className="my-3 border-gray-200" />}

              {/* Settings & Support */}
              <div className="space-y-1">
                {sidebarOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Settings & Support</div>}
                <SidebarItem icon="settings" label="Settings" collapsed={!sidebarOpen} />
                <SidebarItem icon="help" label="Help & Support" collapsed={!sidebarOpen} />
                <SidebarItem icon="feedback" label="Send Feedback" collapsed={!sidebarOpen} />
                <SidebarItem icon="notifications" label="Notifications" collapsed={!sidebarOpen} />
                <SidebarItem icon="profile" label="Profile" collapsed={!sidebarOpen} />
                <SidebarItem icon="billing" label="Billing" collapsed={!sidebarOpen} />
                <SidebarItem icon="integrations" label="Integrations" collapsed={!sidebarOpen} />
                <SidebarItem icon="api" label="API Access" collapsed={!sidebarOpen} />
                <SidebarItem icon="security" label="Security" collapsed={!sidebarOpen} />
                <SidebarItem icon="backup" label="Backup & Restore" collapsed={!sidebarOpen} />
              </div>
            </nav>
          </div>
        </aside>

      {/* Main Content */}
      <main className={`transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
          {/* Category Circles Carousel */}
          <div className="bg-white border-b border-gray-200">
            <CategoryCarousel />
          </div>

          {/* Video Grid */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=225&fit=crop"
                title="Enterprise Marketing Strategies for 2024"
                channel="Encreasl Business"
                views="1.2M views"
                time="3 days ago"
                duration="15:42"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=225&fit=crop"
                title="Data Analytics Dashboard Best Practices"
                channel="Tech Insights"
                views="856K views"
                time="1 week ago"
                duration="22:15"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=225&fit=crop"
                title="Building Scalable E-commerce Platforms"
                channel="Development Pro"
                views="2.1M views"
                time="2 weeks ago"
                duration="18:30"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1553877522-43269d4ea984?w=400&h=225&fit=crop"
                title="Customer Retention Strategies That Work"
                channel="Growth Hacker"
                views="743K views"
                time="4 days ago"
                duration="12:08"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=225&fit=crop"
                title="AI in Business: Practical Applications"
                channel="Future Tech"
                views="1.8M views"
                time="1 day ago"
                duration="25:45"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop"
                title="Leadership in Remote Teams"
                channel="Management Today"
                views="924K views"
                time="5 days ago"
                duration="16:22"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=225&fit=crop"
                title="Digital Transformation Success Stories"
                channel="Enterprise Solutions"
                views="1.5M views"
                time="1 week ago"
                duration="20:15"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=400&h=225&fit=crop"
                title="Performance Marketing ROI Optimization"
                channel="Marketing Metrics"
                views="687K views"
                time="3 days ago"
                duration="14:33"
              />

              {/* Additional Video Cards for Scrolling Test */}
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=225&fit=crop"
                title="Advanced SEO Techniques for 2024"
                channel="Digital Marketing Pro"
                views="1.3M views"
                time="2 days ago"
                duration="19:45"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?w=400&h=225&fit=crop"
                title="Cloud Infrastructure Best Practices"
                channel="DevOps Masters"
                views="987K views"
                time="1 week ago"
                duration="23:12"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=400&h=225&fit=crop"
                title="Product Management Fundamentals"
                channel="Product School"
                views="756K views"
                time="4 days ago"
                duration="17:28"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=400&h=225&fit=crop"
                title="Financial Planning for Startups"
                channel="Startup Finance"
                views="1.1M views"
                time="6 days ago"
                duration="21:35"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=400&h=225&fit=crop"
                title="UX Design Principles That Convert"
                channel="Design Academy"
                views="834K views"
                time="3 days ago"
                duration="15:50"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1553484771-371a605b060b?w=400&h=225&fit=crop"
                title="Supply Chain Optimization Strategies"
                channel="Operations Excellence"
                views="692K views"
                time="1 week ago"
                duration="18:42"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1556155092-490a1ba16284?w=400&h=225&fit=crop"
                title="Cybersecurity for Small Business"
                channel="Security First"
                views="1.4M views"
                time="2 days ago"
                duration="24:18"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=225&fit=crop"
                title="Machine Learning in Marketing"
                channel="AI Marketing"
                views="1.7M views"
                time="5 days ago"
                duration="26:33"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=225&fit=crop"
                title="Agile Project Management Mastery"
                channel="Project Pro"
                views="923K views"
                time="1 week ago"
                duration="20:15"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1553877522-43269d4ea984?w=400&h=225&fit=crop"
                title="Brand Building in Digital Age"
                channel="Brand Masters"
                views="1.2M views"
                time="3 days ago"
                duration="22:47"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=225&fit=crop"
                title="Sales Funnel Optimization Guide"
                channel="Sales Academy"
                views="876K views"
                time="4 days ago"
                duration="19:22"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop"
                title="Remote Work Productivity Hacks"
                channel="Work Smart"
                views="1.5M views"
                time="2 days ago"
                duration="16:55"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=225&fit=crop"
                title="Content Marketing ROI Measurement"
                channel="Marketing Metrics"
                views="734K views"
                time="1 week ago"
                duration="18:12"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=400&h=225&fit=crop"
                title="Customer Success Best Practices"
                channel="Success Stories"
                views="1.1M views"
                time="5 days ago"
                duration="21:08"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=225&fit=crop"
                title="Business Intelligence Dashboard Design"
                channel="BI Experts"
                views="892K views"
                time="3 days ago"
                duration="23:45"
              />
              <VideoCard
                thumbnail="https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?w=400&h=225&fit=crop"
                title="Scaling SaaS Businesses"
                channel="SaaS Growth"
                views="1.6M views"
                time="1 day ago"
                duration="25:30"
              />
            </div>
          </div>
        </main>
    </div>
  );
}
