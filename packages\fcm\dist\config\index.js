/**
 * FCM Configuration Utilities
 *
 * Shared configuration utilities for Firebase Cloud Messaging
 * that work across different environments and applications.
 */
import { z } from 'zod';
// ========================================
// VALIDATION SCHEMAS
// ========================================
const FCMConfigSchema = z.object({
    apiKey: z.string().min(1, "API key is required"),
    authDomain: z.string().min(1, "Auth domain is required"),
    projectId: z.string().min(1, "Project ID is required"),
    storageBucket: z.string().min(1, "Storage bucket is required"),
    messagingSenderId: z.string().min(1, "Messaging sender ID is required"),
    appId: z.string().min(1, "App ID is required"),
    vapidKey: z.string().optional(),
});
const FCMEnvironmentSchema = z.object({
    NEXT_PUBLIC_FIREBASE_API_KEY: z.string().min(1),
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: z.string().min(1),
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: z.string().min(1),
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: z.string().min(1),
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: z.string().min(1),
    NEXT_PUBLIC_FIREBASE_APP_ID: z.string().min(1),
    NEXT_PUBLIC_FIREBASE_VAPID_KEY: z.string().optional(),
});
// ========================================
// CONFIGURATION FUNCTIONS
// ========================================
/**
 * Create FCM configuration from environment variables
 */
export function createFCMConfig(env) {
    const config = {
        apiKey: env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
        authDomain: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
        projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || '',
        storageBucket: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
        messagingSenderId: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
        appId: env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
        vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
    };
    const result = FCMConfigSchema.safeParse(config);
    if (!result.success) {
        throw new Error(`Invalid FCM configuration: ${result.error.issues.map(i => i.message).join(', ')}`);
    }
    return result.data;
}
/**
 * Get FCM configuration from process.env
 */
export function getFCMConfig() {
    return createFCMConfig(process.env);
}
/**
 * Validate FCM configuration
 */
export function validateFCMConfig(config) {
    const result = FCMConfigSchema.safeParse(config);
    if (result.success) {
        const warnings = [];
        // Check for optional but recommended fields
        if (!config.vapidKey) {
            warnings.push("VAPID key is not configured - web push notifications may not work properly");
        }
        return {
            isValid: true,
            errors: [],
            warnings,
        };
    }
    return {
        isValid: false,
        errors: result.error.issues.map(issue => `${issue.path.join('.')}: ${issue.message}`),
        warnings: [],
    };
}
/**
 * Validate FCM environment variables
 */
export function validateFCMEnvironment(env = process.env) {
    const result = FCMEnvironmentSchema.safeParse(env);
    if (result.success) {
        const warnings = [];
        // Check for optional but recommended fields
        if (!env.NEXT_PUBLIC_FIREBASE_VAPID_KEY) {
            warnings.push("NEXT_PUBLIC_FIREBASE_VAPID_KEY is not set - web push notifications may not work properly");
        }
        return {
            isValid: true,
            errors: [],
            warnings,
        };
    }
    return {
        isValid: false,
        errors: result.error.issues.map(issue => `${issue.path.join('.')}: ${issue.message}`),
        warnings: [],
    };
}
// ========================================
// ENVIRONMENT HELPERS
// ========================================
/**
 * Check if FCM is properly configured in the current environment
 */
export function isFCMConfigured(env = process.env) {
    const validation = validateFCMEnvironment(env);
    return validation.isValid;
}
/**
 * Get FCM configuration status with detailed information
 */
export function getFCMConfigStatus(env = process.env) {
    const validation = validateFCMEnvironment(env);
    return {
        isConfigured: validation.isValid,
        hasVapidKey: !!env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
        projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || null,
        errors: validation.errors,
        warnings: validation.warnings,
    };
}
// ========================================
// DEFAULT CONFIGURATIONS
// ========================================
/**
 * Default FCM configuration for development
 */
export const DEFAULT_FCM_CONFIG = {
    // These should be overridden by environment variables
    apiKey: '',
    authDomain: '',
    projectId: '',
    storageBucket: '',
    messagingSenderId: '',
    appId: '',
};
/**
 * FCM feature flags and settings
 */
export const FCM_SETTINGS = {
    // Enable/disable FCM features
    ENABLE_BACKGROUND_MESSAGING: true,
    ENABLE_NOTIFICATION_ANALYTICS: true,
    ENABLE_TOPIC_SUBSCRIPTIONS: true,
    // Default notification settings
    DEFAULT_NOTIFICATION_ICON: '/icons/notification-icon.png',
    DEFAULT_NOTIFICATION_BADGE: '/icons/notification-badge.png',
    // Service worker settings
    SERVICE_WORKER_PATH: '/firebase-messaging-sw.js',
    SERVICE_WORKER_SCOPE: '/',
    // Retry settings
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_DELAY_MS: 1000,
    // Token refresh settings
    TOKEN_REFRESH_INTERVAL_MS: 24 * 60 * 60 * 1000, // 24 hours
};
// ========================================
// TOPIC CONFIGURATIONS
// ========================================
/**
 * Predefined FCM topics for the application
 */
export const FCM_TOPICS = {
    // Admin topics
    ADMIN_NOTIFICATIONS: 'admin-notifications',
    ADMIN_ALERTS: 'admin-alerts',
    ADMIN_REPORTS: 'admin-reports',
    // User topics
    USER_UPDATES: 'user-updates',
    NEWSLETTER: 'newsletter',
    CAMPAIGNS: 'campaigns',
    MARKETING: 'marketing',
    // System topics
    SYSTEM_ALERTS: 'system-alerts',
    MAINTENANCE: 'maintenance',
    // General topics
    GENERAL: 'general',
    ANNOUNCEMENTS: 'announcements',
};
/**
 * Topic configurations with metadata
 */
export const TOPIC_CONFIGS = {
    [FCM_TOPICS.ADMIN_NOTIFICATIONS]: {
        displayName: 'Admin Notifications',
        description: 'Important notifications for administrators',
        defaultSubscribed: false,
        requiresPermission: true,
    },
    [FCM_TOPICS.ADMIN_ALERTS]: {
        displayName: 'Admin Alerts',
        description: 'Critical alerts requiring immediate attention',
        defaultSubscribed: false,
        requiresPermission: true,
    },
    [FCM_TOPICS.USER_UPDATES]: {
        displayName: 'Account Updates',
        description: 'Updates about your account and profile',
        defaultSubscribed: true,
        requiresPermission: false,
    },
    [FCM_TOPICS.NEWSLETTER]: {
        displayName: 'Newsletter',
        description: 'Weekly newsletter and updates',
        defaultSubscribed: false,
        requiresPermission: false,
    },
    [FCM_TOPICS.CAMPAIGNS]: {
        displayName: 'Marketing Campaigns',
        description: 'New marketing campaigns and promotions',
        defaultSubscribed: false,
        requiresPermission: false,
    },
    [FCM_TOPICS.MARKETING]: {
        displayName: 'Marketing Updates',
        description: 'Marketing tips, insights, and best practices',
        defaultSubscribed: false,
        requiresPermission: false,
    },
    [FCM_TOPICS.SYSTEM_ALERTS]: {
        displayName: 'System Alerts',
        description: 'System maintenance and service updates',
        defaultSubscribed: true,
        requiresPermission: false,
    },
    [FCM_TOPICS.GENERAL]: {
        displayName: 'General Notifications',
        description: 'General updates and announcements',
        defaultSubscribed: true,
        requiresPermission: false,
    },
};
//# sourceMappingURL=index.js.map