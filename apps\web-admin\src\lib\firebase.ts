/**
 * Firebase Configuration for Admin App
 * Uses shared @encreasl/auth package for enterprise-level authentication
 */

import {
  initializeAdminFirebase,
  createFirebaseConfigFromEnv,
  getFirebaseAuth as getSharedFirebaseAuth,
  getCurrentFirebaseApp
} from '@encreasl/auth';
import { getFirestore, Firestore } from 'firebase/firestore';
import { FirebaseApp } from 'firebase/app';
import { Auth } from 'firebase/auth';

// Initialize Firebase for admin app using shared configuration
let app: FirebaseApp | null = null;
let db: Firestore | null = null;

/**
 * Firebase services interface for type safety
 */
interface FirebaseServices {
  app: FirebaseApp;
  auth: Auth;
  db: Firestore;
}

/**
 * Initialize Firebase services for admin app
 * Uses the shared auth package for consistent configuration
 */
export function initializeFirebase(): FirebaseServices {
  try {
    // Initialize admin Firebase app using shared package
    const firebaseConfig = createFirebaseConfigFromEnv(true);
    app = initializeAdminFirebase(firebaseConfig);

    // Initialize Firestore
    if (!db && app) {
      db = getFirestore(app);
    }

    // Ensure all services are initialized
    if (!app) {
      throw new Error('Failed to initialize Firebase app');
    }
    if (!db) {
      throw new Error('Failed to initialize Firestore');
    }

    const auth = getSharedFirebaseAuth();
    if (!auth) {
      throw new Error('Failed to initialize Firebase Auth');
    }

    return {
      app,
      auth,
      db,
    };
  } catch (error) {
    console.error('Failed to initialize Firebase for admin app:', error);
    throw error;
  }
}

// Initialize Firebase services immediately
let firebase: FirebaseServices | null = null;

// Lazy initialization function
function getFirebaseServices(): FirebaseServices {
  if (!firebase) {
    firebase = initializeFirebase();
  }
  return firebase;
}

// Export getters that initialize on first access
export const getAdminFirebaseApp = (): FirebaseApp => getFirebaseServices().app;
export const getAdminFirebaseAuth = (): Auth => getFirebaseServices().auth;
export const getAdminFirebaseDb = (): Firestore => getFirebaseServices().db;

// Legacy exports for backward compatibility
export const firebaseApp = getAdminFirebaseApp();
export const firebaseAuth = getAdminFirebaseAuth();
export const firebaseDb = getAdminFirebaseDb();

// Export Firebase config for reference
export const firebaseConfig = createFirebaseConfigFromEnv(true);

// Helper function to check if Firebase is initialized
export function isFirebaseInitialized(): boolean {
  return !!getCurrentFirebaseApp();
}

// Helper function to get Firebase app instance
export function getFirebaseAppInstance(): FirebaseApp {
  const app = getCurrentFirebaseApp();
  if (!app) {
    throw new Error('Firebase is not initialized. Call initializeFirebase() first.');
  }
  return app;
}
