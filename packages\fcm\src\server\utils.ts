/**
 * @file packages/fcm/src/server/utils.ts
 * @description FCM server utilities
 */

import 'server-only';

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Format a notification message for FCM
 */
export function formatNotificationMessage(
  title: string,
  body: string,
  data?: Record<string, string>
) {
  return {
    title: title.substring(0, 100), // FCM title limit
    body: body.substring(0, 500), // FCM body limit
    data: data || {},
  };
}

/**
 * Validate topic name for FCM (re-exported from main utils)
 */
export { validateTopicName } from '../utils';

/**
 * Sanitize data payload for FCM
 */
export function sanitizeDataPayload(data: Record<string, unknown>): Record<string, string> {
  const sanitized: Record<string, string> = {};
  
  for (const [key, value] of Object.entries(data)) {
    // FCM data payload must be string key-value pairs
    if (typeof key === 'string' && key.length > 0) {
      sanitized[key] = String(value);
    }
  }
  
  return sanitized;
}

/**
 * Create a batch of device tokens for multicast
 */
export function createTokenBatch(tokens: string[], batchSize: number = 500): string[][] {
  const batches: string[][] = [];
  
  for (let i = 0; i < tokens.length; i += batchSize) {
    batches.push(tokens.slice(i, i + batchSize));
  }
  
  return batches;
}

/**
 * Generate a unique message ID
 */
export function generateMessageId(): string {
  return `fcm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Log FCM operation
 */
export function logFCMOperation(
  operation: string,
  target: string,
  success: boolean,
  error?: string
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    operation,
    target,
    success,
    error,
  };
  
  if (success) {
    console.log('FCM Operation Success:', logData);
  } else {
    console.error('FCM Operation Failed:', logData);
  }
}
