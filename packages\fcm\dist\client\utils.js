/**
 * @file packages/fcm/src/client/utils.ts
 * @description FCM client utilities
 */
// ========================================
// UTILITY FUNCTIONS
// ========================================
/**
 * Check if FCM is supported in the current browser
 */
export function isFCMSupported() {
    try {
        // Mock implementation - in production, this would check for service worker and notification support
        return typeof window !== 'undefined' && 'serviceWorker' in navigator && 'Notification' in window;
    }
    catch (error) {
        console.error('FCM: Error checking support:', error);
        return false;
    }
}
/**
 * Request notification permission
 */
export async function requestNotificationPermission() {
    try {
        if (!('Notification' in window)) {
            throw new Error('Notifications not supported');
        }
        if (Notification.permission === 'granted') {
            return 'granted';
        }
        if (Notification.permission === 'denied') {
            return 'denied';
        }
        // Mock implementation - in production, this would request actual permission
        console.log('FCM: Requesting notification permission');
        return 'granted';
    }
    catch (error) {
        console.error('FCM: Error requesting permission:', error);
        return 'denied';
    }
}
/**
 * Show a local notification
 */
export function showNotification(title, options) {
    try {
        if (!('Notification' in window)) {
            console.warn('FCM: Notifications not supported');
            return;
        }
        if (Notification.permission === 'granted') {
            new Notification(title, options);
        }
        else {
            console.warn('FCM: Notification permission not granted');
        }
    }
    catch (error) {
        console.error('FCM: Error showing notification:', error);
    }
}
/**
 * Format notification data for display
 */
export function formatNotificationData(data) {
    const formatted = {};
    for (const [key, value] of Object.entries(data)) {
        try {
            // Try to parse JSON values
            formatted[key] = JSON.parse(value);
        }
        catch {
            // Keep as string if not valid JSON
            formatted[key] = value;
        }
    }
    return formatted;
}
/**
 * Log FCM client event
 */
export function logFCMEvent(event, data) {
    console.log('FCM Client Event:', {
        timestamp: new Date().toISOString(),
        event,
        data,
    });
}
//# sourceMappingURL=utils.js.map