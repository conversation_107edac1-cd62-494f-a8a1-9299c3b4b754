{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../../src/client/hooks.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AACjE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAkB,MAAM,oBAAoB,CAAC;AACvF,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAOtD,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AAEzC,2CAA2C;AAC3C,gBAAgB;AAChB,2CAA2C;AAE3C;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,MAAiB;IACtC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAe;QAC/C,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,KAAK;QAClB,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;IACvC,MAAM,cAAc,GAAG,MAAM,CAAsB,IAAI,CAAC,CAAC;IAEzD,8BAA8B;IAC9B,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACxC,IAAI,CAAC;YACH,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAE9E,8BAA8B;YAC9B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClC,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;oBAChC,GAAG,IAAI;oBACP,WAAW,EAAE,KAAK;oBAClB,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,iDAAiD;iBACzD,CAAC,CAAC,CAAC;gBACJ,OAAO;YACT,CAAC;YAED,uCAAuC;YACvC,IAAI,CAAC,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,IAAI,SAAS,CAAC,EAAE,CAAC;gBACnE,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;oBAChC,GAAG,IAAI;oBACP,WAAW,EAAE,KAAK;oBAClB,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,gDAAgD;iBACxD,CAAC,CAAC,CAAC;gBACJ,OAAO;YACT,CAAC;YAED,qDAAqD;YACrD,IAAI,GAAG,CAAC;YACR,MAAM,YAAY,GAAG,OAAO,EAAE,CAAC;YAC/B,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC;YAED,uBAAuB;YACvB,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;YACpC,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;YAEjC,0BAA0B;YAC1B,IAAI,eAAe,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,mBAAmB,EAAE;wBACvE,KAAK,EAAE,YAAY,CAAC,oBAAoB;qBACzC,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,OAAO,EAAE,CAAC;oBACjB,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;YAED,gCAAgC;YAChC,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;YAE3C,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;gBAChC,GAAG,IAAI;gBACP,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,IAAI;gBACjB,UAAU;gBACV,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC,CAAC;YAEJ,0BAA0B;YAC1B,MAAM,WAAW,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC,OAAuB,EAAE,EAAE;gBACnE,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,OAAO,CAAC,CAAC;gBAErD,kCAAkC;gBAClC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;oBACzB,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,cAAc,CAAC,OAAO,GAAG,WAAW,CAAC;QAEvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;gBAChC,GAAG,IAAI;gBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B;gBAC1E,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC,CAAC;QACN,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,kCAAkC;IAClC,MAAM,iBAAiB,GAAG,WAAW,CAAC,KAAK,IAAsB,EAAE;QACjE,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAC1D,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;YAE5D,OAAO,UAAU,KAAK,SAAS,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;gBAChC,GAAG,IAAI;gBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B;aAC/E,CAAC,CAAC,CAAC;YACJ,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;IAExB,gBAAgB;IAChB,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,EAAE,QAAiB,EAA0B,EAAE;QACpF,IAAI,CAAC;YACH,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBAC5D,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE;gBACjD,QAAQ,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ;aACtC,CAAC,CAAC;YAEH,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;gBAChC,GAAG,IAAI;gBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB;aACtE,CAAC,CAAC,CAAC;YACJ,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAExC,qBAAqB;IACrB,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,EAAE,KAAa,EAAoB,EAAE;QAC7E,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,MAAM,aAAa,EAAE,CAAC;YACnD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,8CAA8C;YAC9C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,oBAAoB,EAAE;gBACjD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;aACvC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;gBAChC,GAAG,IAAI;gBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B;aAC/E,CAAC,CAAC,CAAC;YACJ,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;IAEjC,yBAAyB;IACzB,MAAM,oBAAoB,GAAG,WAAW,CAAC,KAAK,EAAE,KAAa,EAAoB,EAAE;QACjF,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,MAAM,aAAa,EAAE,CAAC;YACnD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,kDAAkD;YAClD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,sBAAsB,EAAE;gBACnD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;aACvC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,QAAQ,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,CAAC;gBAChC,GAAG,IAAI;gBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,kCAAkC;aACnF,CAAC,CAAC,CAAC;YACJ,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;IAEjC,qBAAqB;IACrB,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,GAAG,EAAE;YACV,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,GAAG,KAAK;QACR,UAAU;QACV,iBAAiB;QACjB,QAAQ,EAAE,aAAa;QACvB,gBAAgB;QAChB,oBAAoB;KACrB,CAAC;AACJ,CAAC;AAED,2CAA2C;AAC3C,+BAA+B;AAC/B,2CAA2C;AAE3C;;GAEG;AACH,MAAM,UAAU,yBAAyB;IACvC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAA8B;QAC9D,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,KAAK;QAClB,SAAS,EAAE,IAAI;QACf,KAAK,EAAE,IAAI;KACZ,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,8BAA8B;QAC9B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,QAAQ,CAAC;gBACP,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,0CAA0C;aAClD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC;gBACP,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,6BAA6B;aACrC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,QAAQ,CAAC;YACP,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,KAAK;YAChB,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,iBAAiB,GAAG,WAAW,CAAC,KAAK,IAAsB,EAAE;QACjE,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,QAAQ,CAAC,CAAC,IAAiC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAE7F,MAAM,UAAU,GAAG,MAAM,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAE1D,QAAQ,CAAC,CAAC,IAAiC,EAAE,EAAE,CAAC,CAAC;gBAC/C,GAAG,IAAI;gBACP,UAAU;gBACV,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC,CAAC;YAEJ,OAAO,UAAU,KAAK,SAAS,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,CAAC,IAAiC,EAAE,EAAE,CAAC,CAAC;gBAC/C,GAAG,IAAI;gBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B;gBAC9E,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC,CAAC;YACJ,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;IAExB,OAAO;QACL,GAAG,KAAK;QACR,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAED,2CAA2C;AAC3C,gCAAgC;AAChC,2CAA2C;AAE3C;;GAEG;AACH,MAAM,UAAU,0BAA0B;IACxC,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAiC,IAAI,CAAC,CAAC;IACrF,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAExD,mBAAmB;IACnB,MAAM,eAAe,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC7C,IAAI,CAAC;YACH,YAAY,CAAC,IAAI,CAAC,CAAC;YACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEf,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAC;YACrD,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,QAAQ,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC;QAC9E,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,qBAAqB;IACrB,MAAM,iBAAiB,GAAG,WAAW,CAAC,KAAK,EAAE,OAAyC,EAAE,EAAE;QACxF,IAAI,CAAC;YACH,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEf,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,sBAAsB,EAAE;gBACnD,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;aAC9B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,MAAM,kBAAkB,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACjD,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAEnC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,QAAQ,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;YAC9E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,4BAA4B;IAC5B,SAAS,CAAC,GAAG,EAAE;QACb,eAAe,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,OAAO;QACL,WAAW;QACX,SAAS;QACT,KAAK;QACL,eAAe;QACf,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAED,2CAA2C;AAC3C,mBAAmB;AACnB,2CAA2C;AAE3C;;GAEG;AACH,SAAS,gBAAgB,CAAC,YAAiB;IACzC,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;QAC1C,IAAI,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE;YACnC,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,yBAAyB;YACjE,KAAK,EAAE,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,0BAA0B;YACpE,GAAG,EAAE,YAAY,CAAC,GAAG;YACrB,IAAI,EAAE,YAAY,CAAC,IAAI;SACxB,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}