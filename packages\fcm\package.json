{"name": "@encreasl/fcm", "version": "1.0.0", "description": "Shared Firebase Cloud Messaging utilities and configurations for Encreasl monorepo", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "files": ["dist/**/*"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./client": {"types": "./dist/client/index.d.ts", "import": "./dist/client/index.js", "require": "./dist/client/index.js"}, "./server": {"types": "./dist/server/index.d.ts", "import": "./dist/server/index.js", "require": "./dist/server/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js", "require": "./dist/types/index.js"}}, "dependencies": {"firebase": "^10.7.1", "firebase-admin": "^12.0.0", "zod": "^3.22.4"}, "devDependencies": {"@encreasl/typescript-config": "workspace:*", "@encreasl/eslint-config": "workspace:*", "@types/node": "^20.10.6", "@types/react": "^19", "typescript": "^5.3.3", "eslint": "^8.56.0"}, "peerDependencies": {"@encreasl/auth": "workspace:*", "@encreasl/env": "workspace:*", "react": "^18.0.0 || ^19.0.0"}, "keywords": ["firebase", "fcm", "cloud-messaging", "notifications", "monorepo", "encreasl"], "author": "Encreasl Team", "license": "UNLICENSED", "private": true}