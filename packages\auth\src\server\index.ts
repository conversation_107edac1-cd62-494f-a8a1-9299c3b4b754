/**
 * @encreasl/auth/server - Server-side Authentication Package
 * 
 * Provides Firebase Admin SDK utilities for server-side authentication
 * operations. This module should only be imported on the server side.
 */

// ========================================
// SERVER-SIDE EXPORTS ONLY
// ========================================

// Firebase Admin configuration and utilities
export {
  initializeFirebaseAdmin,
  getFirebaseAdmin,
  getAdminAuth,
  getAdminDb,
  getAdminFirestore,
  verifyIdToken,
  verifyAdminUser,
  getCurrentAdminUser,
  setCustomUserClaims,
} from '../firebase-admin';

// ========================================
// TYPE EXPORTS
// ========================================

// Re-export Firebase Admin types from our firebase-admin module
export type {
  AdminApp,
  AdminAuth,
  AdminFirestore,
} from '../firebase-admin';

// Re-export Firebase Admin types directly
export type {
  UserRecord,
  CreateRequest,
  UpdateRequest,
  DecodedIdToken,
  ListUsersResult,
} from 'firebase-admin/auth';

// ========================================
// VALIDATION
// ========================================

// Ensure this module is only used server-side
if (typeof window !== 'undefined') {
  throw new Error(
    '@encreasl/auth/server should only be imported on the server side. ' +
    'Use @encreasl/auth for client-side authentication.'
  );
}
