/**
 * @file apps/web-admin/src/server/utils/export-utils.ts
 * @description Admin-specific data export utilities
 */

import 'server-only';
import type { AuditLogEntry, AnalyticsFilters } from '../types/admin-server-types';

// ========================================
// ADMIN EXPORT TYPES
// ========================================

export type AdminExportOptions = {
  format: 'csv' | 'json' | 'xlsx';
  filters?: AnalyticsFilters;
  includeMetadata?: boolean;
  compression?: boolean;
  filename?: string;
};

export type ExportResult = {
  downloadUrl: string;
  filename: string;
  expiresAt: string;
  size: number;
  recordCount: number;
};

// ========================================
// EXPORT UTILITIES
// ========================================

/**
 * Generate user data export for admin
 */
export function generateUserDataExport(
  users: Record<string, unknown>[],
  options: AdminExportOptions
): ExportResult {
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = options.filename || `users-export-${timestamp}.${options.format}`;
  
  // In a real implementation, you would:
  // 1. Format the data according to the export format
  // 2. Generate the actual file
  // 3. Store it temporarily with a download URL
  // 4. Set up cleanup for expired files
  
  console.log('Generating user data export:', {
    userCount: users.length,
    format: options.format,
    filename,
  });
  
  return {
    downloadUrl: `/api/admin/exports/download/${crypto.randomUUID()}`,
    filename,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    size: users.length * 1024, // Mock size calculation
    recordCount: users.length,
  };
}

/**
 * Generate audit log export for admin
 */
export function generateAuditLogExport(
  logs: AuditLogEntry[],
  options: AdminExportOptions
): ExportResult {
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = options.filename || `audit-logs-${timestamp}.${options.format}`;
  
  console.log('Generating audit log export:', {
    logCount: logs.length,
    format: options.format,
    filename,
  });
  
  return {
    downloadUrl: `/api/admin/exports/download/${crypto.randomUUID()}`,
    filename,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    size: logs.length * 512, // Mock size calculation
    recordCount: logs.length,
  };
}

/**
 * Format export data based on format type
 */
export function formatExportData(
  data: Record<string, unknown>[],
  format: 'csv' | 'json' | 'xlsx',
  includeMetadata: boolean = false
): string {
  const metadata = includeMetadata ? {
    exportedAt: new Date().toISOString(),
    recordCount: data.length,
    format,
  } : null;
  
  switch (format) {
    case 'json':
      return JSON.stringify({
        ...(metadata && { metadata }),
        data,
      }, null, 2);
      
    case 'csv':
      if (data.length === 0) return '';
      
      const headers = Object.keys(data[0]);
      const csvHeaders = headers.join(',');
      const csvRows = data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape CSV values that contain commas or quotes
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      );
      
      const csvContent = [csvHeaders, ...csvRows].join('\n');
      
      if (metadata) {
        const metadataComment = `# Export Metadata: ${JSON.stringify(metadata)}\n`;
        return metadataComment + csvContent;
      }
      
      return csvContent;
      
    case 'xlsx':
      // In a real implementation, you would use a library like 'xlsx' to generate Excel files
      console.log('XLSX export would be generated here with a proper library');
      return JSON.stringify({ message: 'XLSX export not implemented', data });
      
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}

/**
 * Validate export options
 */
export function validateExportOptions(options: Partial<AdminExportOptions>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!options.format) {
    errors.push('Export format is required');
  } else if (!['csv', 'json', 'xlsx'].includes(options.format)) {
    errors.push('Export format must be csv, json, or xlsx');
  }
  
  if (options.filename && !/^[a-zA-Z0-9._-]+$/.test(options.filename)) {
    errors.push('Filename contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Generate secure download token
 */
export function generateDownloadToken(): string {
  return crypto.randomUUID();
}

/**
 * Calculate estimated export size
 */
export function estimateExportSize(
  recordCount: number,
  format: 'csv' | 'json' | 'xlsx',
  avgRecordSize: number = 1024
): number {
  const baseSize = recordCount * avgRecordSize;
  
  switch (format) {
    case 'csv':
      return Math.round(baseSize * 0.7); // CSV is more compact
    case 'json':
      return Math.round(baseSize * 1.2); // JSON has more overhead
    case 'xlsx':
      return Math.round(baseSize * 0.8); // Excel compression
    default:
      return baseSize;
  }
}
