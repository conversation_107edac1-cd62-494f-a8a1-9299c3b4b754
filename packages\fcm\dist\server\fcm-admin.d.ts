/**
 * @file packages/fcm/src/server/fcm-admin.ts
 * @description Firebase Cloud Messaging admin utilities
 */
import 'server-only';
export type FCMTopicMessage = {
    title: string;
    body: string;
    data?: Record<string, string>;
    imageUrl?: string;
    clickAction?: string;
};
export type FCMSendResult = {
    success: boolean;
    messageId?: string;
    error?: string;
};
/**
 * Send a message to a topic
 */
export declare function sendToTopic(topic: string, message: FCMTopicMessage): Promise<FCMSendResult>;
/**
 * Send a message to a specific device token
 */
export declare function sendToDevice(token: string, message: FCMTopicMessage): Promise<FCMSendResult>;
/**
 * Send a message to multiple device tokens
 */
export declare function sendToDevices(tokens: string[], message: FCMTopicMessage): Promise<Array<FCMSendResult>>;
/**
 * Subscribe a device token to a topic
 */
export declare function subscribeToTopic(token: string, topic: string): Promise<FCMSendResult>;
/**
 * Unsubscribe a device token from a topic
 */
export declare function unsubscribeFromTopic(token: string, topic: string): Promise<FCMSendResult>;
/**
 * Validate a device token
 */
export declare function validateToken(token: string): Promise<boolean>;
//# sourceMappingURL=fcm-admin.d.ts.map