{"$schema": "https://openapi.vercel.sh/vercel.json", "buildCommand": "cd ../.. && pnpm turbo run build --filter=@encreasl/cms", "installCommand": "cd ../.. && pnpm install", "framework": "nextjs", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}, "src/app/(payload)/admin/[[...segments]]/page.tsx": {"maxDuration": 30}}, "env": {"NODE_OPTIONS": "--no-deprecation --max-old-space-size=8000"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/admin/:path*", "destination": "/admin/:path*"}]}