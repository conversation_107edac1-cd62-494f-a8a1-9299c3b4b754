/**
 * @file packages/fcm/src/server/utils.ts
 * @description FCM server utilities
 */
import 'server-only';
/**
 * Format a notification message for FCM
 */
export declare function formatNotificationMessage(title: string, body: string, data?: Record<string, string>): {
    title: string;
    body: string;
    data: Record<string, string>;
};
/**
 * Validate topic name for FCM (re-exported from main utils)
 */
export { validateTopicName } from '../utils';
/**
 * Sanitize data payload for FCM
 */
export declare function sanitizeDataPayload(data: Record<string, unknown>): Record<string, string>;
/**
 * Create a batch of device tokens for multicast
 */
export declare function createTokenBatch(tokens: string[], batchSize?: number): string[][];
/**
 * Generate a unique message ID
 */
export declare function generateMessageId(): string;
/**
 * Log FCM operation
 */
export declare function logFCMOperation(operation: string, target: string, success: boolean, error?: string): void;
//# sourceMappingURL=utils.d.ts.map