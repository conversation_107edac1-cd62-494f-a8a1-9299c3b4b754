/**
 * @encreasl/fcm - Shared Firebase Cloud Messaging Package
 *
 * Provides FCM utilities, configurations, and React hooks
 * for the Encreasl monorepo. Supports both client and server-side usage.
 */
export * from './types';
export { initializeFCM, getFCMToken, subscribeToTopic as clientSubscribeToTopic, unsubscribeFromTopic as clientUnsubscribeFromTopic, isFCMSupported, requestNotificationPermission, showNotification, formatNotificationData as clientFormatNotificationData, logFCMEvent, useFCM, useNotificationPermission, useNotificationPreferences, } from './client';
export { sendToTopic, sendToDevice, sendToDevices, subscribeToTopic as serverSubscribeToTopic, unsubscribeFromTopic as serverUnsubscribeFromTopic, validateToken, formatNotificationMessage, sanitizeDataPayload, createTokenBatch, generateMessageId, logFCMOperation, } from './server';
export * from './utils';
export * from './config';
export type { FCMConfig, FCMMessage, NotificationPayload, NotificationData, NotificationPriority, NotificationType, FCMDevice, FCMTopic, NotificationPreferences, } from './types';
export { validateFCMConfig, createFCMMessage, formatNotificationData, } from './utils';
export { createFCMConfig, getFCMConfig, validateFCMEnvironment, } from './config';
//# sourceMappingURL=index.d.ts.map