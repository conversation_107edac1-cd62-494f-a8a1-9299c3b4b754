/**
 * @file apps/web-admin/src/server/index.ts
 * @description Server-side exports for the Web Admin App
 * 
 * This file serves as the main entry point for all server-side functionality
 * specific to the admin application. It re-exports server actions, utilities,
 * and services that are used throughout the admin app.
 */

import 'server-only';

// ========================================
// ADMIN SERVER ACTIONS
// ========================================

// User management server actions
export {
  createUser,
  updateUser,
  deleteUser,
  getUserById,
  getUsersList,
  banUser,
  unbanUser,
  type UserManagementData,
  type UserListFilters,
} from './actions/user-management-actions';

// Admin operations server actions
export {
  createAdminUser,
  updateAdminUser,
  deleteAdminUser,
  assignAdminRole,
  revokeAdminRole,
  validateAdminPermissions,
  type AdminUserData,
  type AdminRoleAssignment,
} from './actions/admin-actions';

// Audit logging server actions
export {
  logAdminAction,
  getAuditLogs,
  exportAuditLogs,
  type AuditLogEntry,
  type AuditLogFilters,
} from './actions/audit-actions';

// Dashboard server actions
export {
  getDashboardStats,
  type DashboardStats,
} from './actions/dashboard-actions';

// ========================================
// ADMIN SERVER UTILITIES
// ========================================

// Admin-specific server utilities
export {
  validateAdminRequest,
  checkAdminPermissions,
  sanitizeAdminInput,
  formatAdminResponse,
  handleAdminError,
} from './utils/admin-utils';

// Permission management utilities
export {
  hasPermission,
  checkResourceAccess,
  validateRolePermissions,
  type Permission,
  type ResourceAccess,
} from './utils/permissions';

// Data validation utilities
export {
  validateUserData,
  validateAdminData,
  validateSystemSettings,
  type ValidationResult,
} from './utils/validation';

// ========================================
// ADMIN SERVER SERVICES
// ========================================

// User risk assessment service (admin-specific business logic)
export {
  UserRiskAssessmentService,
  type RiskAssessment,
  type RiskFactor,
  type SecurityEvent,
  type ComplianceViolation,
} from './services/user-risk-assessment-service';

// Compliance reporting service (admin-specific compliance)
export {
  ComplianceReportingService,
  type ComplianceReport,
  type ComplianceFinding,
  type ComplianceMetrics,
  type DataSubjectRequest,
} from './services/compliance-reporting-service';

// ========================================
// ADMIN SERVER UTILITIES
// ========================================

// Data export utilities (admin-specific)
export {
  generateUserDataExport,
  generateAuditLogExport,
  formatExportData,
  type AdminExportOptions,
} from './utils/export-utils';

// ========================================
// TYPE EXPORTS
// ========================================

// Re-export common admin server types
export type {
  AdminServerActionResult,
  AdminServerError,
  AdminValidationError,
  AdminRequestContext,
} from './types/admin-server-types';
